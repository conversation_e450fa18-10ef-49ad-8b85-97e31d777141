"use client"

import { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import { useToast } from '@/components/ui/use-toast';

interface AuthContextType {
  isAuthenticated: boolean;
  checkAuth: () => boolean;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const { toast } = useToast();

  const checkAuth = (): boolean => {
    const idToken = Cookies.get('id_token');
    const session = Cookies.get('session');
    const token = idToken || session;

    if (!token) {
      return false;
    }

    try {
      // For simple session tokens that aren't JWTs, just check if they exist
      if (token && token.length > 0) {
        return true;
      }

      // If it looks like a JWT (has dots), try to parse it
      if (token.includes('.')) {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Math.floor(Date.now() / 1000);

        if (payload.exp && payload.exp < currentTime) {
          // Token is expired
          return false;
        }
      }

      return true;
    } catch (error) {
      // If we can't parse it, but it exists, consider it valid
      // This handles non-JWT session tokens
      console.warn('Token parsing failed, but token exists:', error);
      return !!(token && token.length > 0);
    }
  };

  const logout = () => {
    // Clear all auth cookies
    Cookies.remove('id_token');
    Cookies.remove('session');
    Cookies.remove('access_token');
    Cookies.remove('refresh_token');
    
    setIsAuthenticated(false);
    
    // Show logout message
    toast({
      title: "Session Expired",
      description: "Your session has expired. Please log in again.",
      variant: "destructive",
    });
    
    // Redirect to login
    router.push('/login');
  };

  const handleTokenExpiration = () => {
    logout();
  };

  useEffect(() => {
    const authCheck = checkAuth();
    setIsAuthenticated(authCheck);
    setIsLoading(false);

    // Don't redirect immediately on mount - let the ProtectedRoute component handle it
    // This prevents redirect loops
  }, []);

  // Set up periodic token validation
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(() => {
      const authCheck = checkAuth();
      if (!authCheck) {
        handleTokenExpiration();
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [isAuthenticated]);

  // Note: We removed the global fetch interception to prevent redirect loops
  // The API client handles 401/403 responses individually

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
          <div className="h-4 w-4 rounded-full bg-purple-600 animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <AuthContext.Provider value={{ isAuthenticated, checkAuth, logout }}>
      {children}
    </AuthContext.Provider>
  );
}
