"use client"

import { useState, useEffect, useRef } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, MapPin, Heart, Share2, Phone, Mail, Building2, Loader2, CheckCircle, X, ChevronLeft, ChevronRight, DollarSign, Home } from "lucide-react"
import 'mapbox-gl/dist/mapbox-gl.css'
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { publicPropertiesService, PublicProperty } from "@/lib/services/public-properties.service"
import { Logo } from "@/components/shared/logo"

export default function PropertyDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const [property, setProperty] = useState<PublicProperty | null>(null)
  const [loading, setLoading] = useState(true)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [modalImageIndex, setModalImageIndex] = useState(0)

  // Map state
  const [mapLoaded, setMapLoaded] = useState(false)
  const [mapError, setMapError] = useState<string | null>(null)
  const mapContainerRef = useRef<HTMLDivElement>(null)
  const mapRef = useRef<any>(null)


  // Fetch property data
  useEffect(() => {
    const fetchProperty = async () => {
      if (!params.id) return

      setLoading(true)
      try {
        const propertyData = await publicPropertiesService.getPropertyById(params.id as string)
        setProperty(propertyData)
      } catch (error) {
        console.error('Error fetching property:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchProperty()
  }, [params.id])

  // Initialize Mapbox map
  useEffect(() => {
    if (!property || !property.coordinates || !mapContainerRef.current || mapRef.current) return

    const MAPBOX_TOKEN = process.env.NEXT_PUBLIC_MAPBOX_TOKEN
    if (!MAPBOX_TOKEN) {
      setMapError('Mapbox token is missing')
      return
    }

    const initializeMap = async () => {
      try {
        // Dynamically import mapbox-gl
        const mapboxgl = await import('mapbox-gl')
        mapboxgl.default.accessToken = MAPBOX_TOKEN

        // Create map centered on property location
        const map = new mapboxgl.default.Map({
          container: mapContainerRef.current!,
          style: 'mapbox://styles/mapbox/streets-v12',
          center: [property.coordinates!.lng, property.coordinates!.lat],
          zoom: 15,
          attributionControl: false
        })

        // Add error handling
        map.on('error', (e) => {
          console.error('Mapbox error:', e)
          setMapError(`Map error: ${e.error?.message || 'Unknown error'}`)
        })

        // Add navigation controls
        map.addControl(new mapboxgl.default.NavigationControl(), 'top-right')

        // Add property marker
        const el = document.createElement('div')
        el.className = 'property-marker'
        el.style.cssText = `
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
          border: 3px solid white;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          font-size: 18px;
          color: white;
          font-weight: bold;
        `
        el.innerHTML = 'V'

        new mapboxgl.default.Marker(el)
          .setLngLat([property.coordinates!.lng, property.coordinates!.lat])
          .addTo(map)

        map.on('load', () => {
          setMapLoaded(true)
        })

        mapRef.current = map

      } catch (error) {
        console.error('Error initializing Mapbox:', error)
        setMapError('Failed to load map')
      }
    }

    initializeMap()

    // Cleanup
    return () => {
      if (mapRef.current) {
        mapRef.current.remove()
        mapRef.current = null
      }
    }
  }, [property])

  // Modal functions
  const openModal = (imageIndex: number) => {
    setModalImageIndex(imageIndex)
    setIsModalOpen(true)
    document.body.style.overflow = 'hidden' // Prevent background scrolling
  }

  const closeModal = () => {
    setIsModalOpen(false)
    document.body.style.overflow = 'unset' // Restore scrolling
  }

  const nextImage = () => {
    if (property && property.images.length > 0) {
      setModalImageIndex((prev) => (prev + 1) % property.images.length)
    }
  }

  const prevImage = () => {
    if (property && property.images.length > 0) {
      setModalImageIndex((prev) => (prev - 1 + property.images.length) % property.images.length)
    }
  }

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isModalOpen) return

      switch (event.key) {
        case 'Escape':
          closeModal()
          break
        case 'ArrowLeft':
          prevImage()
          break
        case 'ArrowRight':
          nextImage()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isModalOpen, property])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading property details...</p>
        </div>
      </div>
    )
  }

  if (!property) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Property Not Found</h2>
          <p className="text-gray-600 mb-6">The property you're looking for doesn't exist or has been removed.</p>
          <Link href="/properties">
            <Button className="bg-purple-600 hover:bg-purple-700">
              Back to Properties
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Enhanced Responsive Header - Same as Apply Page */}
      <div className="bg-white/95 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 sm:h-18 md:h-20">
            <div className="flex items-center gap-2 sm:gap-4 md:gap-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="flex items-center gap-1 sm:gap-2 hover:bg-purple-50 hover:text-purple-700 rounded-xl px-2 sm:px-3 md:px-4 py-2 transition-all duration-200 text-xs sm:text-sm"
              >
                <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="hidden sm:inline">Back</span>
              </Button>
              <div className="scale-75 sm:scale-90 md:scale-100">
                <Logo />
              </div>
            </div>
            <div className="text-center flex-1 mx-2 sm:mx-4">
              <h1 className="text-base sm:text-lg md:text-xl font-bold bg-gradient-to-r from-purple-600 to-purple-800 bg-clip-text text-transparent">
                Property Details
              </h1>
              <p className="text-xs sm:text-sm text-gray-500 mt-0.5 sm:mt-1 hidden sm:block">Explore this rental property</p>
            </div>
            <div className="flex items-center gap-1 sm:gap-2">
              <Link href="/properties">
                <Button
                  variant="ghost"
                  size="sm"
                  className="hover:bg-purple-50 hover:text-purple-700 rounded-xl px-2 sm:px-3 py-2 transition-all duration-200 text-xs sm:text-sm"
                >
                  <Building2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                  <span className="hidden md:inline">Back to Properties</span>
                  <span className="hidden sm:inline md:hidden">Properties</span>
                </Button>
              </Link>
              <Button
                variant="ghost"
                size="sm"
                className="hover:bg-purple-50 hover:text-purple-700 rounded-xl px-2 sm:px-3 py-2 transition-all duration-200 text-xs sm:text-sm"
              >
                <Heart className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                <span className="hidden sm:inline">Save</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="hover:bg-purple-50 hover:text-purple-700 rounded-xl px-2 sm:px-3 py-2 transition-all duration-200 text-xs sm:text-sm"
              >
                <Share2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                <span className="hidden sm:inline">Share</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Beautiful Purple Gradient Hero Section - Same as Apply Page */}
      <div className="bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 text-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 right-0 w-64 h-64 bg-white rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white rounded-full translate-y-24 -translate-x-24"></div>
        </div>

        <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 mb-6">
            <div className="w-10 h-10 sm:w-12 sm:h-12 bg-white/20 rounded-xl sm:rounded-2xl flex items-center justify-center backdrop-blur-sm">
              <Building2 className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl sm:text-2xl font-bold">Property Details</h2>
              <p className="text-purple-100 text-xs sm:text-sm">Explore this beautiful rental property</p>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-white/20">
            <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4 lg:gap-6">
              {/* Property Info */}
              <div className="flex-1">
                <h3 className="text-lg sm:text-xl md:text-2xl font-bold mb-2 sm:mb-3 leading-tight">{property.title}</h3>
                <div className="flex items-start gap-2 text-purple-100 mb-3 sm:mb-4">
                  <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mt-0.5 flex-shrink-0" />
                  <p className="text-sm sm:text-base leading-relaxed">{property.address}</p>
                </div>

                {/* Availability Badge */}
                <div className="mb-4">
                  <div className={`inline-flex items-center gap-2 px-3 py-1.5 rounded-lg text-sm font-medium ${
                    property.isFreeNow
                      ? 'bg-green-500/20 text-green-100 border border-green-400/30'
                      : 'bg-blue-500/20 text-blue-100 border border-blue-400/30'
                  }`}>
                    <div className={`w-2 h-2 rounded-full ${
                      property.isFreeNow ? 'bg-green-400' : 'bg-blue-400'
                    }`}></div>
                    {property.isFreeNow ? 'Available Now' : (() => {
                      if (!property.availableDate) return 'Available Soon';
                      const dateStr = property.availableDate.split(',')[0];
                      const date = new Date(dateStr);
                      return `Available ${date.toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric'
                      })}`;
                    })()}
                  </div>
                </div>

                {/* Quick Features */}
                <div className="flex flex-wrap gap-2">
                  {property.isPetFriendly && (
                    <div className="bg-white/20 text-white px-3 py-1 rounded-lg text-xs font-medium">
                      🐕 Pet Friendly
                    </div>
                  )}
                  {property.isFurnished && (
                    <div className="bg-white/20 text-white px-3 py-1 rounded-lg text-xs font-medium">
                      🛋️ Furnished
                    </div>
                  )}
                  {property.isHasParking && (
                    <div className="bg-white/20 text-white px-3 py-1 rounded-lg text-xs font-medium">
                      🚗 Parking
                    </div>
                  )}
                  {property.isHasElevator && (
                    <div className="bg-white/20 text-white px-3 py-1 rounded-lg text-xs font-medium">
                      🛗 Elevator
                    </div>
                  )}
                  {property.isHasPool && (
                    <div className="bg-white/20 text-white px-3 py-1 rounded-lg text-xs font-medium">
                      🏊‍♂️ Pool
                    </div>
                  )}
                </div>
              </div>

              {/* Price and Stats */}
              <div className="lg:text-right">
                <div className="flex flex-col sm:flex-row lg:flex-col items-start sm:items-center lg:items-end gap-3 sm:gap-4 lg:gap-3 text-white">
                  <div className="flex items-center gap-2 bg-white/20 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2 w-full sm:w-auto">
                    <DollarSign className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                    <span className="font-semibold text-sm sm:text-base">${property.price?.toLocaleString()}/month</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white/20 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2">
                    <Building2 className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                    <span className="text-sm sm:text-base">{property.bedrooms} bed</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white/20 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2">
                    <Home className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                    <span className="text-sm sm:text-base">{property.bathrooms} bath</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-6">
            {/* Apply Button Section */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
              <div className="flex items-start justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Ready to Apply?</h2>
                  <p className="text-gray-600">
                    Submit your application and get one step closer to your new home
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-2xl flex items-center justify-center">
                  <span className="text-2xl">🏠</span>
                </div>
              </div>

              <Link href={`/properties/${params.id}/apply`}>
                <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white text-lg py-4 rounded-xl font-semibold">
                  Apply for this Property
                </Button>
              </Link>
            </div>

            {/* Property Photos Gallery */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
              <div className="flex gap-2 h-[400px]">
                {/* Main Large Image */}
                <div className="flex-1 relative group cursor-pointer" onClick={() => openModal(0)}>
                  <img
                    src={property.images[0]}
                    alt={property.title}
                    className="w-full h-full object-cover transition-transform group-hover:scale-105"
                  />
                  {/* Badge overlay */}
                  <div className="absolute top-4 left-4">
                    <div className="bg-gray-900/80 text-white text-sm px-3 py-1 rounded-lg flex items-center gap-2">
                      <Building2 className="h-4 w-4" />
                      <span>New Listing</span>
                    </div>
                  </div>
                  {/* Click to expand overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-200 flex items-center justify-center">
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/90 rounded-full p-3">
                      <span className="text-gray-900 text-sm font-medium">Click to expand</span>
                    </div>
                  </div>
                </div>

                {/* Grid of 4 smaller images */}
                {property.images.length > 1 && (
                  <div className="w-[300px] grid grid-cols-2 gap-2">
                    {property.images.slice(1, 5).map((image, index) => (
                      <div
                        key={index + 1}
                        className="relative group cursor-pointer overflow-hidden"
                        onClick={() => openModal(index + 1)}
                      >
                        <img
                          src={image}
                          alt={`View ${index + 2}`}
                          className="w-full h-full object-cover transition-transform group-hover:scale-105"
                        />
                        {/* Show count on last image if there are more */}
                        {index === 3 && property.images.length > 5 && (
                          <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                            <div className="text-white text-center">
                              <span className="text-2xl font-bold">+{property.images.length - 5}</span>
                              <p className="text-sm">more photos</p>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Image counter and navigation */}
              {property.images.length > 1 && (
                <div className="p-4 bg-gray-50 border-t">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-600">
                      {property.images.length} photos available
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={() => setCurrentImageIndex(Math.max(0, currentImageIndex - 1))}
                        disabled={currentImageIndex === 0}
                        className="p-2 rounded-lg border border-gray-200 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ArrowLeft className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => setCurrentImageIndex(Math.min(property.images.length - 1, currentImageIndex + 1))}
                        disabled={currentImageIndex === property.images.length - 1}
                        className="p-2 rounded-lg border border-gray-200 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ArrowLeft className="h-4 w-4 rotate-180" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* About This Property Card */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
              <div className="flex items-start justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">About This Property</h2>
                <div className="w-12 h-12 bg-purple-100 rounded-2xl flex items-center justify-center">
                  <Building2 className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <p className="text-gray-600 leading-relaxed text-lg">{property.description}</p>
            </div>

            {/* Property Features Card */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
              <div className="flex items-start justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Property Features</h2>
                <div className="w-12 h-12 bg-blue-100 rounded-2xl flex items-center justify-center">
                  <span className="text-2xl">✨</span>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {[
                  { label: 'Pet Friendly', value: property.isPetFriendly, icon: '🐕' },
                  { label: 'Furnished', value: property.isFurnished, icon: '🛋️' },
                  { label: 'Parking', value: property.isHasParking, icon: '🚗' },
                  { label: 'Elevator', value: property.isHasElevator, icon: '🛗' },
                  { label: 'Pool', value: property.isHasPool, icon: '🏊‍♂️' },
                  { label: 'Accessible', value: property.isAdaptedForReducedMobility, icon: '♿' }
                ].map((feature, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                    <div className="flex items-center gap-3">
                      <span className="text-xl">{feature.icon}</span>
                      <span className="text-gray-700 font-medium">{feature.label}</span>
                    </div>
                    <span className={`text-sm font-semibold px-3 py-1 rounded-full ${
                      feature.value
                        ? 'bg-green-100 text-green-700'
                        : 'bg-gray-100 text-gray-500'
                    }`}>
                      {feature.value ? 'Yes' : 'No'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Required Documents Card */}
            {property.requiredVerifications && property.requiredVerifications.length > 0 && (
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                <div className="flex items-start justify-between mb-6">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Required Documents</h2>
                    <p className="text-gray-600">
                      Please prepare these documents before applying
                    </p>
                  </div>
                  <div className="w-12 h-12 bg-orange-100 rounded-2xl flex items-center justify-center">
                    <span className="text-2xl">📋</span>
                  </div>
                </div>
                <div className="space-y-3">
                  {property.requiredVerifications.map((verification, index) => (
                    <div key={index} className="flex items-center gap-4 p-4 bg-gray-50 rounded-xl">
                      <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                      <span className="text-gray-700 font-medium">{verification}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Location & Map Card */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
              <div className="flex items-start justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Location</h2>
                  <p className="text-gray-600">
                    Explore the neighborhood and nearby amenities
                  </p>
                </div>
                <div className="w-12 h-12 bg-blue-100 rounded-2xl flex items-center justify-center">
                  <MapPin className="h-6 w-6 text-blue-600" />
                </div>
              </div>

              {/* Address */}
              <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl mb-6">
                <MapPin className="h-5 w-5 text-gray-500" />
                <span className="text-gray-700 font-medium">{property.address}</span>
              </div>

              {/* Mapbox Container */}
              <div className="relative h-[400px] bg-gray-100 rounded-xl overflow-hidden">
                {property.coordinates ? (
                  <>
                    <div
                      ref={mapContainerRef}
                      className="w-full h-full rounded-xl"
                      style={{ minHeight: '400px' }}
                    />
                    {!mapLoaded && !mapError && (
                      <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-xl">
                        <div className="text-center">
                          <Loader2 className="h-8 w-8 text-blue-600 animate-spin mx-auto mb-2" />
                          <p className="text-gray-600 text-sm">Loading map...</p>
                        </div>
                      </div>
                    )}
                    {mapError && (
                      <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-50 rounded-xl">
                        <div className="text-center">
                          <div className="w-16 h-16 bg-red-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <MapPin className="h-8 w-8 text-red-600" />
                          </div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">Map Error</h3>
                          <p className="text-gray-600 text-sm">{mapError}</p>
                          <p className="text-xs text-gray-500 mt-2">{property.address}</p>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <MapPin className="h-8 w-8 text-blue-600" />
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Location</h3>
                      <p className="text-gray-600 text-sm">Map coordinates not available</p>
                      <p className="text-xs text-gray-500 mt-2">{property.address}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Contact Owner Card */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
              <div className="flex items-start justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Contact Owner</h2>
                  <p className="text-gray-600">
                    Get in touch with the property owner for more information
                  </p>
                </div>
                <div className="w-12 h-12 bg-green-100 rounded-2xl flex items-center justify-center">
                  <span className="text-2xl">👤</span>
                </div>
              </div>

              <div className="flex items-center gap-4 mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center text-white text-xl font-bold">
                  {property.landlord.name.charAt(0).toUpperCase()}
                </div>
                <div className="flex-1">
                  <div className="text-lg font-bold text-gray-900">{property.landlord.name}</div>
                  <div className="flex items-center gap-2 mt-1">
                    {property.landlord.isVerified ? (
                      <div className="flex items-center gap-2 text-sm text-green-600">
                        <CheckCircle className="h-4 w-4" />
                        <span className="font-medium">Verified Owner</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <div className="h-4 w-4 rounded-full border-2 border-gray-400"></div>
                        <span className="font-medium">Not Verified</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Verification Status Card */}
              <div className={`p-4 rounded-xl mb-6 ${
                property.landlord.isVerified
                  ? 'bg-green-50 border border-green-200'
                  : 'bg-yellow-50 border border-yellow-200'
              }`}>
                <div className="flex items-start gap-3">
                  {property.landlord.isVerified ? (
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  ) : (
                    <div className="h-5 w-5 rounded-full border-2 border-yellow-500 mt-0.5"></div>
                  )}
                  <div>
                    <h4 className={`font-semibold text-sm ${
                      property.landlord.isVerified ? 'text-green-800' : 'text-yellow-800'
                    }`}>
                      {property.landlord.isVerified ? 'Verified Profile' : 'Profile Not Verified'}
                    </h4>
                    <p className={`text-xs mt-1 ${
                      property.landlord.isVerified ? 'text-green-700' : 'text-yellow-700'
                    }`}>
                      {property.landlord.isVerified
                        ? 'This owner has been verified by our team. You can trust their identity and property information.'
                        : 'This owner profile has not been verified yet. Please exercise caution when contacting.'
                      }
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
                  <Phone className="h-5 w-5 text-gray-500" />
                  <span className="text-gray-700 font-medium">{property.landlord.phone}</span>
                </div>
                <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
                  <Mail className="h-5 w-5 text-gray-500" />
                  <span className="text-gray-700 font-medium">{property.landlord.email}</span>
                </div>
              </div>
            </div>
        </div>
      </div>

      {/* Full-Screen Image Modal */}
      {isModalOpen && property && (
        <div className="fixed inset-0 z-50 bg-black/95 flex items-center justify-center">
          {/* Close button */}
          <button
            onClick={closeModal}
            className="absolute top-4 right-4 z-10 p-2 bg-white/10 hover:bg-white/20 rounded-full transition-colors"
          >
            <X className="h-6 w-6 text-white" />
          </button>

          {/* Image counter */}
          <div className="absolute top-4 left-4 z-10 bg-black/50 text-white px-3 py-1 rounded-lg text-sm">
            {modalImageIndex + 1} of {property.images.length}
          </div>

          {/* Previous button */}
          {property.images.length > 1 && (
            <button
              onClick={prevImage}
              className="absolute left-4 top-1/2 -translate-y-1/2 z-10 p-3 bg-white/10 hover:bg-white/20 rounded-full transition-colors"
            >
              <ChevronLeft className="h-8 w-8 text-white" />
            </button>
          )}

          {/* Next button */}
          {property.images.length > 1 && (
            <button
              onClick={nextImage}
              className="absolute right-4 top-1/2 -translate-y-1/2 z-10 p-3 bg-white/10 hover:bg-white/20 rounded-full transition-colors"
            >
              <ChevronRight className="h-8 w-8 text-white" />
            </button>
          )}

          {/* Main image */}
          <div className="relative max-w-7xl max-h-full mx-4">
            <img
              src={property.images[modalImageIndex]}
              alt={`${property.title} - Image ${modalImageIndex + 1}`}
              className="max-w-full max-h-[90vh] object-contain"
            />
          </div>

          {/* Image thumbnails */}
          {property.images.length > 1 && (
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2 max-w-full overflow-x-auto px-4">
              {property.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setModalImageIndex(index)}
                  className={`flex-shrink-0 w-16 h-12 rounded-lg overflow-hidden border-2 transition-all ${
                    index === modalImageIndex
                      ? 'border-white'
                      : 'border-white/30 hover:border-white/60'
                  }`}
                >
                  <img
                    src={image}
                    alt={`Thumbnail ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          )}

          {/* Click outside to close */}
          <div
            className="absolute inset-0 -z-10"
            onClick={closeModal}
          />
        </div>
      )}
    </div>
  )
}
