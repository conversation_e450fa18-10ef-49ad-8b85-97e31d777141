"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { CheckCircle, Home, Mail, Phone, Calendar, ArrowLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { publicPropertiesService, PublicProperty } from "@/lib/services/public-properties.service"
import { Logo } from "@/components/shared/logo"
import Link from "next/link"

export default function ApplicationSuccessPage() {
  const params = useParams()
  const [property, setProperty] = useState<PublicProperty | null>(null)

  useEffect(() => {
    const fetchProperty = async () => {
      if (!params.id) return

      try {
        const propertyData = await publicPropertiesService.getPropertyById(params.id as string)
        setProperty(propertyData)
      } catch (error) {
        console.error('Error fetching property:', error)
      }
    }

    fetchProperty()
  }, [params.id])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link href="/properties">
                <Button variant="ghost" size="sm" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Back to Properties
                </Button>
              </Link>
              <Logo />
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Success Message */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="h-12 w-12 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Application Submitted Successfully!
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            Thank you for your interest in this property. Your application has been received and is being reviewed.
          </p>
        </div>

        {/* Property Summary */}
        {property && (
          <Card className="mb-8 border-0 shadow-lg rounded-2xl overflow-hidden">
            <div className="bg-gradient-to-r from-purple-600 to-purple-700 p-6 text-white">
              <h2 className="text-xl font-bold mb-2">Applied for</h2>
              <h3 className="text-lg mb-2">{property.title}</h3>
              <p className="text-purple-100 mb-2">{property.address}</p>
              <div className="flex items-center gap-4 text-sm">
                <span>${property.price?.toLocaleString()}/month</span>
                <span>•</span>
                <span>{property.bedrooms} bed</span>
                <span>•</span>
                <span>{property.bathrooms} bath</span>
              </div>
            </div>
          </Card>
        )}

        {/* Next Steps */}
        <Card className="border-0 shadow-lg rounded-2xl mb-8">
          <CardContent className="p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">What happens next?</h2>
            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-purple-600 font-semibold text-sm">1</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">Application Review</h3>
                  <p className="text-gray-600">The property owner will review your application within 24-48 hours.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-purple-600 font-semibold text-sm">2</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">Initial Contact</h3>
                  <p className="text-gray-600">If your application meets the requirements, you'll be contacted for next steps.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-purple-600 font-semibold text-sm">3</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">Property Viewing</h3>
                  <p className="text-gray-600">Schedule a viewing to see the property in person and ask any questions.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-purple-600 font-semibold text-sm">4</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">Final Decision</h3>
                  <p className="text-gray-600">Complete any additional requirements and finalize your rental agreement.</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card className="border-0 shadow-lg rounded-2xl mb-8">
          <CardContent className="p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Need Help?</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Mail className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Email Support</p>
                  <p className="text-sm text-gray-600"><EMAIL></p>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Phone className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Phone Support</p>
                  <p className="text-sm text-gray-600">(*************</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/properties">
            <Button
              variant="outline"
              className="w-full sm:w-auto border-gray-300 text-gray-700 hover:bg-gray-50 rounded-xl px-6 py-3"
            >
              <Home className="h-4 w-4 mr-2" />
              Browse More Properties
            </Button>
          </Link>
          
          <Link href={`/properties/${params.id}`}>
            <Button
              className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white rounded-xl px-6 py-3"
            >
              View Property Details
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
