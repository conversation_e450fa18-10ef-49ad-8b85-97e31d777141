"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, User, Mail, Phone, Home, Calendar, DollarSign, Heart, MessageSquare, Globe, Loader2, MapPin } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { publicPropertiesService, PublicProperty } from "@/lib/services/public-properties.service"
import { Logo } from "@/components/shared/logo"
import Link from "next/link"

// Enums based on backend structure
enum PreferredCommunicationCanal {
  EMAIL = "EMAIL",
  SMS = "SMS",
  BOTH = "BOTH"
}

enum Language {
  ENGLISH = "en",
  FRENCH = "fr"
}

enum AnnualIncomeRange {
  RANGE_10K_20K = "RANGE20K",
  RANGE_20K_30K = "RANGE30K",
  RANGE_30K_40K = "RANGE40K",
  RANGE_40K_50K = "RANGE50K",
  RANGE_50K_60K = "RANGE60K",
  RANGE_60K_70K = "RANGE70K",
  RANGE_70K_80K = "RANGE80K",
  RANGE_80K_90K = "RANGE90K",
  RANGE_90K_100K = "RANGE100K",
  RANGE_100K_110K = "RANGE110K",
  RANGE_110K_120K = "RANGE120K",
  RANGE_120K_130K = "RANGE130K",
  RANGE_130K_140K = "RANGE140K",
  RANGE_140K_150K = "RANGE150K",
  RANGE_150K_PLUS = "RANGE150K_PLUS"
}

interface ApplicationFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  preferredContactMethod: PreferredCommunicationCanal
  preferredLanguage: Language
  address: string
  familySize: number
  reasonOfMoving: string
  isPetOwner: boolean
  petDescription: string
  isAvailableNow: boolean
  earliestMoveInDate: string
  latestMoveInDate: string
  annualIncomeRange: AnnualIncomeRange
  wantsPropertyRecommendations: boolean
}

export default function PropertyApplicationPage() {
  const params = useParams()
  const router = useRouter()
  const [property, setProperty] = useState<PublicProperty | null>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [formData, setFormData] = useState<ApplicationFormData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    preferredContactMethod: PreferredCommunicationCanal.EMAIL,
    preferredLanguage: Language.FRENCH,
    address: "",
    familySize: 1,
    reasonOfMoving: "",
    isPetOwner: false,
    petDescription: "",
    isAvailableNow: true,
    earliestMoveInDate: "",
    latestMoveInDate: "",
    annualIncomeRange: AnnualIncomeRange.RANGE_30K_40K,
    wantsPropertyRecommendations: true
  })

  // Fetch property data
  useEffect(() => {
    const fetchProperty = async () => {
      if (!params.id) return

      setLoading(true)
      try {
        const propertyData = await publicPropertiesService.getPropertyById(params.id as string)
        setProperty(propertyData)
      } catch (error) {
        console.error('Error fetching property:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchProperty()
  }, [params.id])

  const handleInputChange = (field: keyof ApplicationFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)

    try {
      // Prepare the request body according to backend requirements
      const applicationData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone || null,
        preferredContactMethod: formData.preferredContactMethod,
        preferredLanguage: formData.preferredLanguage,
        address: formData.address || null,
        familySize: formData.familySize,
        reasonOfMoving: formData.reasonOfMoving || null,
        isPetOwner: formData.isPetOwner,
        petDescription: formData.isPetOwner ? formData.petDescription : null,
        isAvailableNow: formData.isAvailableNow,
        EarliestMoveInDate: !formData.isAvailableNow && formData.earliestMoveInDate ? formData.earliestMoveInDate : null,
        latestMoveInDate: !formData.isAvailableNow && formData.latestMoveInDate ? formData.latestMoveInDate : null,
        annualIncomeRange: formData.annualIncomeRange,
        acceptReceiveRecommendation: formData.wantsPropertyRecommendations
      }

      console.log('Submitting application:', applicationData)

      // Send POST request to the backend
      const response = await fetch(`http://localhost:8081/public/properties/apply/${params.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(applicationData)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
      }

      // Handle successful response - check if response has content
      let result = null
      const contentType = response.headers.get('content-type')
      if (contentType && contentType.includes('application/json')) {
        result = await response.json()
      }

      console.log('Application submitted successfully:', result || 'Success (no response body)')

      // Show success message and redirect
      alert('Application submitted successfully! The property owner will review your application.')
      router.push(`/properties/${params.id}`)
    } catch (error) {
      console.error('Error submitting application:', error)
      alert(`Error submitting application: ${error instanceof Error ? error.message : 'Please try again.'}`)
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading property details...</p>
        </div>
      </div>
    )
  }

  if (!property) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">Property not found</p>
          <Link href="/properties">
            <Button variant="outline">Back to Properties</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-purple-50">
      {/* Enhanced Responsive Header */}
      <div className="bg-white/95 backdrop-blur-md border-b border-gray-200/50 sticky top-0 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 sm:h-18 md:h-20">
            <div className="flex items-center gap-2 sm:gap-4 md:gap-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.back()}
                className="flex items-center gap-1 sm:gap-2 hover:bg-purple-50 hover:text-purple-700 rounded-xl px-2 sm:px-3 md:px-4 py-2 transition-all duration-200 text-xs sm:text-sm"
              >
                <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="hidden sm:inline">Back</span>
              </Button>
              <div className="scale-75 sm:scale-90 md:scale-100">
                <Logo />
              </div>
            </div>
            <div className="text-center flex-1 mx-2 sm:mx-4">
              <h1 className="text-base sm:text-lg md:text-xl font-bold bg-gradient-to-r from-purple-600 to-purple-800 bg-clip-text text-transparent">
                Property Application
              </h1>
              <p className="text-xs sm:text-sm text-gray-500 mt-0.5 sm:mt-1 hidden sm:block">Complete your rental application</p>
            </div>
            <div className="w-8 sm:w-12 md:w-20"></div> {/* Responsive spacer */}
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-3 sm:px-4 md:px-6 lg:px-8 py-6 sm:py-8 md:py-12">
        {/* Enhanced Responsive Property Summary */}
        <Card className="mb-8 sm:mb-10 md:mb-12 border-0 shadow-2xl rounded-2xl sm:rounded-3xl overflow-hidden bg-white/80 backdrop-blur-sm">
          <div className="bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 p-4 sm:p-6 md:p-8 text-white relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 right-0 w-64 h-64 bg-white rounded-full -translate-y-32 translate-x-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-white rounded-full translate-y-24 -translate-x-24"></div>
            </div>

            <div className="relative z-10">
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-white/20 rounded-xl sm:rounded-2xl flex items-center justify-center backdrop-blur-sm">
                  <Home className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                </div>
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold">Applying for</h2>
                  <p className="text-purple-100 text-xs sm:text-sm">Complete your application below</p>
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-white/20">
                <h3 className="text-lg sm:text-xl md:text-2xl font-bold mb-2 sm:mb-3 leading-tight">{property.title}</h3>
                <div className="flex items-start gap-2 text-purple-100 mb-3 sm:mb-4">
                  <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mt-0.5 flex-shrink-0" />
                  <p className="text-sm sm:text-base md:text-lg leading-relaxed">{property.address}</p>
                </div>

                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 md:gap-6 text-white">
                  <div className="flex items-center gap-2 bg-white/20 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2 w-full sm:w-auto">
                    <DollarSign className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                    <span className="font-semibold text-sm sm:text-base">${property.price?.toLocaleString()}/month</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white/20 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2">
                    <User className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                    <span className="text-sm sm:text-base">{property.bedrooms} bed</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white/20 rounded-lg sm:rounded-xl px-3 sm:px-4 py-1.5 sm:py-2">
                    <Home className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
                    <span className="text-sm sm:text-base">{property.bathrooms} bath</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Enhanced Responsive Application Form */}
        <form onSubmit={handleSubmit} className="space-y-6 sm:space-y-8 md:space-y-10">
          {/* Personal Information */}
          <Card className="border-0 shadow-xl rounded-2xl sm:rounded-3xl bg-white/90 backdrop-blur-sm overflow-hidden">
            <CardHeader className="pb-4 sm:pb-6 bg-gradient-to-r from-purple-50 to-purple-100 border-b border-gray-100">
              <CardTitle className="flex flex-col sm:flex-row items-start sm:items-center gap-3 text-xl sm:text-2xl">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl sm:rounded-2xl flex items-center justify-center">
                  <User className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                </div>
                <div>
                  <span className="text-gray-900">Personal Information</span>
                  <p className="text-xs sm:text-sm text-gray-600 font-normal mt-0.5 sm:mt-1">Tell us about yourself</p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 sm:space-y-8 p-4 sm:p-6 md:p-8">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 md:gap-8">
                <div className="space-y-2">
                  <Label htmlFor="firstName" className="text-xs sm:text-sm font-semibold text-gray-800 flex items-center gap-2">
                    <span className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-purple-500 rounded-full"></span>
                    First Name *
                  </Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    className="h-10 sm:h-12 rounded-xl sm:rounded-2xl border-gray-200 focus:border-purple-500 focus:ring-purple-500/20 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 text-sm sm:text-base"
                    placeholder="Enter your first name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName" className="text-xs sm:text-sm font-semibold text-gray-800 flex items-center gap-2">
                    <span className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-purple-500 rounded-full"></span>
                    Last Name *
                  </Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    className="h-10 sm:h-12 rounded-xl sm:rounded-2xl border-gray-200 focus:border-purple-500 focus:ring-purple-500/20 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 text-sm sm:text-base"
                    placeholder="Enter your last name"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 md:gap-8">
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-xs sm:text-sm font-semibold text-gray-800 flex items-center gap-2">
                    <Mail className="w-3 h-3 text-purple-500" />
                    Email Address *
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="h-10 sm:h-12 rounded-xl sm:rounded-2xl border-gray-200 focus:border-purple-500 focus:ring-purple-500/20 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 text-sm sm:text-base"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-xs sm:text-sm font-semibold text-gray-800 flex items-center gap-2">
                    <Phone className="w-3 h-3 text-purple-500" />
                    Phone Number
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className="h-10 sm:h-12 rounded-xl sm:rounded-2xl border-gray-200 focus:border-purple-500 focus:ring-purple-500/20 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 text-sm sm:text-base"
                    placeholder="(*************"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address" className="text-xs sm:text-sm font-semibold text-gray-800 flex items-center gap-2">
                  <Home className="w-3 h-3 text-purple-500" />
                  Current Address
                </Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  className="h-10 sm:h-12 rounded-xl sm:rounded-2xl border-gray-200 focus:border-purple-500 focus:ring-purple-500/20 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 text-sm sm:text-base"
                  placeholder="123 Main Street, Montreal, QC"
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 md:gap-8">
                <div className="space-y-2">
                  <Label className="text-xs sm:text-sm font-semibold text-gray-800 flex items-center gap-2">
                    <MessageSquare className="w-3 h-3 text-purple-500" />
                    Preferred Contact Method
                  </Label>
                  <Select
                    value={formData.preferredContactMethod}
                    onValueChange={(value) => handleInputChange('preferredContactMethod', value)}
                  >
                    <SelectTrigger className="h-10 sm:h-12 rounded-xl sm:rounded-2xl border-gray-200 focus:border-purple-500 focus:ring-purple-500/20 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 text-sm sm:text-base">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="rounded-xl border-gray-200 shadow-xl">
                      <SelectItem value={PreferredCommunicationCanal.EMAIL} className="rounded-lg text-sm">📧 Email</SelectItem>
                      <SelectItem value={PreferredCommunicationCanal.SMS} className="rounded-lg text-sm">� SMS</SelectItem>
                      <SelectItem value={PreferredCommunicationCanal.BOTH} className="rounded-lg text-sm">📧💬 Both Email & SMS</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label className="text-xs sm:text-sm font-semibold text-gray-800 flex items-center gap-2">
                    <Globe className="w-3 h-3 text-purple-500" />
                    Preferred Language
                  </Label>
                  <Select
                    value={formData.preferredLanguage}
                    onValueChange={(value) => handleInputChange('preferredLanguage', value)}
                  >
                    <SelectTrigger className="h-10 sm:h-12 rounded-xl sm:rounded-2xl border-gray-200 focus:border-purple-500 focus:ring-purple-500/20 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 text-sm sm:text-base">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="rounded-xl border-gray-200 shadow-xl">
                      <SelectItem value={Language.ENGLISH} className="rounded-lg text-sm">🇺🇸 English</SelectItem>
                      <SelectItem value={Language.FRENCH} className="rounded-lg text-sm">🇫🇷 French</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Household Information */}
          <Card className="border-0 shadow-xl rounded-2xl sm:rounded-3xl bg-white/90 backdrop-blur-sm overflow-hidden">
            <CardHeader className="pb-4 sm:pb-6 bg-gradient-to-r from-purple-50 to-purple-100 border-b border-gray-100">
              <CardTitle className="flex flex-col sm:flex-row items-start sm:items-center gap-3 text-xl sm:text-2xl">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl sm:rounded-2xl flex items-center justify-center">
                  <Home className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                </div>
                <div>
                  <span className="text-gray-900">Household Information</span>
                  <p className="text-xs sm:text-sm text-gray-600 font-normal mt-0.5 sm:mt-1">Tell us about your living situation</p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 sm:space-y-8 p-4 sm:p-6 md:p-8">
              <div className="space-y-2">
                <Label htmlFor="familySize" className="text-xs sm:text-sm font-semibold text-gray-800 flex items-center gap-2">
                  <User className="w-3 h-3 text-purple-500" />
                  Family Size (including yourself)
                </Label>
                <Input
                  id="familySize"
                  type="number"
                  min="1"
                  value={formData.familySize}
                  onChange={(e) => handleInputChange('familySize', parseInt(e.target.value) || 1)}
                  className="h-10 sm:h-12 rounded-xl sm:rounded-2xl border-gray-200 focus:border-purple-500 focus:ring-purple-500/20 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 text-sm sm:text-base"
                  placeholder="1"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="reasonOfMoving" className="text-xs sm:text-sm font-semibold text-gray-800 flex items-center gap-2">
                  <Home className="w-3 h-3 text-purple-500" />
                  Reason for Moving
                </Label>
                <Textarea
                  id="reasonOfMoving"
                  value={formData.reasonOfMoving}
                  onChange={(e) => handleInputChange('reasonOfMoving', e.target.value)}
                  className="min-h-[80px] sm:min-h-[100px] rounded-xl sm:rounded-2xl border-gray-200 focus:border-purple-500 focus:ring-purple-500/20 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 resize-none text-sm sm:text-base"
                  placeholder="Please describe your reason for moving..."
                />
              </div>

              <div className="space-y-2">
                <Label className="text-xs sm:text-sm font-semibold text-gray-800 flex items-center gap-2">
                  <DollarSign className="w-3 h-3 text-purple-500" />
                  Annual Income Range
                </Label>
                <Select
                  value={formData.annualIncomeRange}
                  onValueChange={(value) => handleInputChange('annualIncomeRange', value)}
                >
                  <SelectTrigger className="h-10 sm:h-12 rounded-xl sm:rounded-2xl border-gray-200 focus:border-purple-500 focus:ring-purple-500/20 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 text-sm sm:text-base">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="rounded-xl border-gray-200 shadow-xl">
                    <SelectItem value={AnnualIncomeRange.RANGE_10K_20K} className="rounded-lg text-sm">$10,000 - $20,000</SelectItem>
                    <SelectItem value={AnnualIncomeRange.RANGE_20K_30K} className="rounded-lg text-sm">$20,000 - $30,000</SelectItem>
                    <SelectItem value={AnnualIncomeRange.RANGE_30K_40K} className="rounded-lg text-sm">$30,000 - $40,000</SelectItem>
                    <SelectItem value={AnnualIncomeRange.RANGE_40K_50K} className="rounded-lg text-sm">$40,000 - $50,000</SelectItem>
                    <SelectItem value={AnnualIncomeRange.RANGE_50K_60K} className="rounded-lg text-sm">$50,000 - $60,000</SelectItem>
                    <SelectItem value={AnnualIncomeRange.RANGE_60K_70K} className="rounded-lg text-sm">$60,000 - $70,000</SelectItem>
                    <SelectItem value={AnnualIncomeRange.RANGE_70K_80K} className="rounded-lg text-sm">$70,000 - $80,000</SelectItem>
                    <SelectItem value={AnnualIncomeRange.RANGE_80K_90K} className="rounded-lg text-sm">$80,000 - $90,000</SelectItem>
                    <SelectItem value={AnnualIncomeRange.RANGE_90K_100K} className="rounded-lg text-sm">$90,000 - $100,000</SelectItem>
                    <SelectItem value={AnnualIncomeRange.RANGE_100K_110K} className="rounded-lg text-sm">$100,000 - $110,000</SelectItem>
                    <SelectItem value={AnnualIncomeRange.RANGE_110K_120K} className="rounded-lg text-sm">$110,000 - $120,000</SelectItem>
                    <SelectItem value={AnnualIncomeRange.RANGE_120K_130K} className="rounded-lg text-sm">$120,000 - $130,000</SelectItem>
                    <SelectItem value={AnnualIncomeRange.RANGE_130K_140K} className="rounded-lg text-sm">$130,000 - $140,000</SelectItem>
                    <SelectItem value={AnnualIncomeRange.RANGE_140K_150K} className="rounded-lg text-sm">$140,000 - $150,000</SelectItem>
                    <SelectItem value={AnnualIncomeRange.RANGE_150K_PLUS} className="rounded-lg text-sm">$150,000+</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Pet Information */}
          <Card className="border-0 shadow-xl rounded-2xl sm:rounded-3xl bg-white/90 backdrop-blur-sm overflow-hidden">
            <CardHeader className="pb-4 sm:pb-6 bg-gradient-to-r from-purple-50 to-purple-100 border-b border-gray-100">
              <CardTitle className="flex flex-col sm:flex-row items-start sm:items-center gap-3 text-xl sm:text-2xl">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl sm:rounded-2xl flex items-center justify-center">
                  <Heart className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                </div>
                <div>
                  <span className="text-gray-900">Pet Information</span>
                  <p className="text-xs sm:text-sm text-gray-600 font-normal mt-0.5 sm:mt-1">Tell us about your furry friends</p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 sm:space-y-8 p-4 sm:p-6 md:p-8">
              <div className="flex items-center space-x-3 sm:space-x-4 p-3 sm:p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl sm:rounded-2xl border border-purple-100">
                <Checkbox
                  id="isPetOwner"
                  checked={formData.isPetOwner}
                  onCheckedChange={(checked) => handleInputChange('isPetOwner', checked)}
                  className="w-4 h-4 sm:w-5 sm:h-5 rounded-lg border-2 border-purple-300 data-[state=checked]:bg-purple-500 data-[state=checked]:border-purple-500"
                />
                <Label htmlFor="isPetOwner" className="text-sm sm:text-base font-semibold text-gray-800 flex items-center gap-2 cursor-pointer">
                  <span className="text-base sm:text-lg">🐾</span>
                  I have pets
                </Label>
              </div>

              {formData.isPetOwner && (
                <div className="space-y-2 animate-in slide-in-from-top-2 duration-300">
                  <Label htmlFor="petDescription" className="text-xs sm:text-sm font-semibold text-gray-800 flex items-center gap-2">
                    <Heart className="w-3 h-3 text-purple-500" />
                    Pet Description
                  </Label>
                  <Textarea
                    id="petDescription"
                    value={formData.petDescription}
                    onChange={(e) => handleInputChange('petDescription', e.target.value)}
                    className="min-h-[100px] sm:min-h-[120px] rounded-xl sm:rounded-2xl border-gray-200 focus:border-purple-500 focus:ring-purple-500/20 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 resize-none text-sm sm:text-base"
                    placeholder="🐕 Tell us about your pets: type, breed, size, age, temperament, training status, etc.&#10;&#10;Example: 'I have a 3-year-old Golden Retriever named Max. He's well-trained, friendly, and weighs about 65 lbs. He's up to date on all vaccinations.'"
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Move-in Information */}
          <Card className="border-0 shadow-xl rounded-2xl sm:rounded-3xl bg-white/90 backdrop-blur-sm overflow-hidden">
            <CardHeader className="pb-4 sm:pb-6 bg-gradient-to-r from-purple-50 to-purple-100 border-b border-gray-100">
              <CardTitle className="flex flex-col sm:flex-row items-start sm:items-center gap-3 text-xl sm:text-2xl">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl sm:rounded-2xl flex items-center justify-center">
                  <Calendar className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                </div>
                <div>
                  <span className="text-gray-900">Move-in Information</span>
                  <p className="text-xs sm:text-sm text-gray-600 font-normal mt-0.5 sm:mt-1">When would you like to move in?</p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 sm:space-y-8 p-4 sm:p-6 md:p-8">
              <div className="flex items-center space-x-3 sm:space-x-4 p-3 sm:p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl sm:rounded-2xl border border-purple-100">
                <Checkbox
                  id="isAvailableNow"
                  checked={formData.isAvailableNow}
                  onCheckedChange={(checked) => handleInputChange('isAvailableNow', checked)}
                  className="w-4 h-4 sm:w-5 sm:h-5 rounded-lg border-2 border-purple-300 data-[state=checked]:bg-purple-500 data-[state=checked]:border-purple-500"
                />
                <Label htmlFor="isAvailableNow" className="text-sm sm:text-base font-semibold text-gray-800 flex items-center gap-2 cursor-pointer">
                  <span className="text-base sm:text-lg">⚡</span>
                  I am available to move in immediately
                </Label>
              </div>

              {!formData.isAvailableNow && (
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 md:gap-8 animate-in slide-in-from-top-2 duration-300">
                  <div className="space-y-2">
                    <Label htmlFor="earliestMoveInDate" className="text-xs sm:text-sm font-semibold text-gray-800 flex items-center gap-2">
                      <Calendar className="w-3 h-3 text-purple-500" />
                      Earliest Move-in Date
                    </Label>
                    <div className="relative">
                      <Input
                        id="earliestMoveInDate"
                        type="date"
                        value={formData.earliestMoveInDate}
                        onChange={(e) => handleInputChange('earliestMoveInDate', e.target.value)}
                        className="h-10 sm:h-12 rounded-xl sm:rounded-2xl border-gray-200 focus:border-purple-500 focus:ring-purple-500/20 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 pl-3 sm:pl-4 text-sm sm:text-base"
                      />
                      <div className="absolute right-2 sm:right-3 top-1/2 transform -translate-y-1/2 text-purple-500 pointer-events-none text-sm sm:text-base">
                        📅
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="latestMoveInDate" className="text-xs sm:text-sm font-semibold text-gray-800 flex items-center gap-2">
                      <Calendar className="w-3 h-3 text-purple-500" />
                      Latest Move-in Date
                    </Label>
                    <div className="relative">
                      <Input
                        id="latestMoveInDate"
                        type="date"
                        value={formData.latestMoveInDate}
                        onChange={(e) => handleInputChange('latestMoveInDate', e.target.value)}
                        className="h-10 sm:h-12 rounded-xl sm:rounded-2xl border-gray-200 focus:border-purple-500 focus:ring-purple-500/20 bg-white/80 backdrop-blur-sm shadow-sm hover:shadow-md transition-all duration-200 pl-3 sm:pl-4 text-sm sm:text-base"
                      />
                      <div className="absolute right-2 sm:right-3 top-1/2 transform -translate-y-1/2 text-purple-500 pointer-events-none text-sm sm:text-base">
                        📅
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {!formData.isAvailableNow && (
                <div className="bg-gradient-to-r from-purple-50 to-purple-100 border border-purple-200 rounded-xl sm:rounded-2xl p-3 sm:p-4">
                  <div className="flex items-start gap-2 sm:gap-3">
                    <span className="text-base sm:text-lg">💡</span>
                    <div>
                      <h4 className="font-semibold text-purple-800 mb-1 text-sm sm:text-base">Tip</h4>
                      <p className="text-xs sm:text-sm text-purple-700 leading-relaxed">
                        Providing flexible move-in dates increases your chances of approval. Property owners appreciate tenants who can work with their schedule.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Property Recommendations */}
          <Card className="border-0 shadow-xl rounded-2xl sm:rounded-3xl bg-white/90 backdrop-blur-sm overflow-hidden">
            <CardHeader className="pb-4 sm:pb-6 bg-gradient-to-r from-purple-50 to-purple-100 border-b border-gray-100">
              <CardTitle className="flex flex-col sm:flex-row items-start sm:items-center gap-3 text-xl sm:text-2xl">
                <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl sm:rounded-2xl flex items-center justify-center">
                  <Mail className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                </div>
                <div>
                  <span className="text-gray-900">Property Recommendations</span>
                  <p className="text-xs sm:text-sm text-gray-600 font-normal mt-0.5 sm:mt-1">Stay updated with similar properties</p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 sm:space-y-6 p-4 sm:p-6 md:p-8">
              <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-purple-100">
                <div className="flex flex-col sm:flex-row items-start gap-3 sm:gap-4">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl sm:rounded-2xl flex items-center justify-center">
                      <span className="text-lg sm:text-xl">🏠</span>
                    </div>
                  </div>
                  <div className="flex-1 w-full">
                    <h3 className="text-base sm:text-lg font-bold text-gray-900 mb-2">Get Similar Property Recommendations</h3>
                    <p className="text-gray-600 text-xs sm:text-sm mb-3 sm:mb-4 leading-relaxed">
                      Would you like to receive notifications about properties similar to this one? We'll send you updates about:
                    </p>
                    <ul className="text-xs sm:text-sm text-gray-600 space-y-1.5 sm:space-y-2 mb-4 sm:mb-6">
                      <li className="flex items-start gap-2">
                        <span className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-purple-500 rounded-full mt-1.5 sm:mt-2 flex-shrink-0"></span>
                        <span className="leading-relaxed">Properties in the same area ({property?.address?.split(',').slice(-2).join(',').trim()})</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-purple-500 rounded-full mt-1.5 sm:mt-2 flex-shrink-0"></span>
                        <span className="leading-relaxed">Similar price range (${property?.price ? (property.price * 0.8).toLocaleString() : '0'} - ${property?.price ? (property.price * 1.2).toLocaleString() : '0'})</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-purple-500 rounded-full mt-1.5 sm:mt-2 flex-shrink-0"></span>
                        <span className="leading-relaxed">Same bedroom/bathroom configuration ({property?.bedrooms} bed, {property?.bathrooms} bath)</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <span className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-purple-500 rounded-full mt-1.5 sm:mt-2 flex-shrink-0"></span>
                        <span className="leading-relaxed">Properties that match your preferences</span>
                      </li>
                    </ul>

                    <div className="flex items-center space-x-3 sm:space-x-4 p-3 sm:p-4 bg-white/80 backdrop-blur-sm rounded-lg sm:rounded-xl border border-purple-200">
                      <Checkbox
                        id="wantsPropertyRecommendations"
                        checked={formData.wantsPropertyRecommendations}
                        onCheckedChange={(checked) => handleInputChange('wantsPropertyRecommendations', checked)}
                        className="w-4 h-4 sm:w-5 sm:h-5 rounded-lg border-2 border-purple-300 data-[state=checked]:bg-purple-500 data-[state=checked]:border-purple-500"
                      />
                      <Label htmlFor="wantsPropertyRecommendations" className="text-sm sm:text-base font-semibold text-gray-800 flex items-center gap-2 cursor-pointer">
                        <span className="text-base sm:text-lg">📧</span>
                        <span className="leading-tight">Yes, send me similar property recommendations</span>
                      </Label>
                    </div>

                    <div className="mt-3 sm:mt-4 text-xs text-gray-500 bg-gray-50 rounded-lg sm:rounded-xl p-2 sm:p-3">
                      <span className="flex items-start gap-2">
                        <span className="text-sm">🔒</span>
                        <span className="leading-relaxed">You can unsubscribe at any time. We respect your privacy and won't spam you.</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Enhanced Responsive Submit Section */}
          <Card className="border-0 shadow-xl rounded-2xl sm:rounded-3xl bg-gradient-to-br from-purple-50 via-white to-blue-50 overflow-hidden">
            <CardContent className="p-4 sm:p-6 md:p-8">
              <div className="text-center mb-4 sm:mb-6">
                <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-1 sm:mb-2">Ready to Submit?</h3>
                <p className="text-sm sm:text-base text-gray-600">Review your information and submit your application</p>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center">
                <Button
                  type="button"
                  onClick={() => router.back()}
                  className="w-full sm:w-auto bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 rounded-xl sm:rounded-2xl px-6 sm:px-8 py-3 sm:py-4 h-auto font-semibold text-sm sm:text-base transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                  Cancel
                </Button>

                <Button
                  type="submit"
                  disabled={submitting}
                  className="w-full sm:w-auto bg-gradient-to-r from-purple-600 via-purple-700 to-purple-800 hover:from-purple-700 hover:via-purple-800 hover:to-purple-900 text-white px-8 sm:px-12 py-3 sm:py-4 rounded-xl sm:rounded-2xl font-bold shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] h-auto text-sm sm:text-base"
                >
                  {submitting ? (
                    <>
                      <Loader2 className="h-4 w-4 sm:h-5 sm:w-5 animate-spin mr-2 sm:mr-3" />
                      <span className="hidden sm:inline">Submitting Application...</span>
                      <span className="sm:hidden">Submitting...</span>
                    </>
                  ) : (
                    <>
                      <Calendar className="h-4 w-4 sm:h-5 sm:w-5 mr-2 sm:mr-3" />
                      <span className="hidden sm:inline">Submit Application</span>
                      <span className="sm:hidden">Submit</span>
                    </>
                  )}
                </Button>
              </div>

              <div className="mt-4 sm:mt-6 text-center">
                <p className="text-xs text-gray-500 bg-gray-50 rounded-lg sm:rounded-xl p-2 sm:p-3 leading-relaxed">
                  🔒 Your information is secure and will only be shared with the property owner
                </p>
              </div>
            </CardContent>
          </Card>
        </form>

        {/* Simplified Privacy & Data Security Notice */}
        <div className="mt-8 sm:mt-10 md:mt-12 bg-gray-50 border border-gray-200 rounded-xl sm:rounded-2xl p-4 sm:p-6 shadow-sm">
          <div className="flex flex-col sm:flex-row items-start gap-3 sm:gap-4">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-100 rounded-xl sm:rounded-2xl flex items-center justify-center">
                <span className="text-lg sm:text-xl">🔒</span>
              </div>
            </div>
            <div className="flex-1 w-full">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-3">
                <h3 className="text-base sm:text-lg font-bold text-gray-900">Your Privacy & Data Security</h3>
                <div className="flex items-center gap-1 bg-gray-100 text-gray-700 text-xs font-semibold px-2 py-1 rounded-full w-fit">
                  <span className="w-1.5 h-1.5 bg-gray-500 rounded-full"></span>
                  Secure
                </div>
              </div>

              <div className="space-y-3 text-sm text-gray-700 leading-relaxed">
                <div className="flex items-start gap-3">
                  <span className="text-gray-500 mt-0.5">🇨🇦</span>
                  <p>
                    <strong>Canadian Data Protection:</strong> All your personal information is securely stored in Canada and protected under Canadian privacy laws (PIPEDA).
                  </p>
                </div>

                <div className="flex items-start gap-3">
                  <span className="text-gray-500 mt-0.5">🏠</span>
                  <p>
                    <strong>Shared with Property Owner:</strong> Your application details will only be shared with the property owner to evaluate your rental application.
                  </p>
                </div>

                <div className="flex items-start gap-3">
                  <span className="text-gray-500 mt-0.5">🗑️</span>
                  <p>
                    <strong>Automatic Data Removal:</strong> If the property owner selects another tenant, your personal information will be automatically removed from their access and securely deleted from our systems within 30 days.
                  </p>
                </div>

                <div className="flex items-start gap-3">
                  <span className="text-gray-500 mt-0.5">👤</span>
                  <p>
                    <strong>Your Control:</strong> You can request to delete your information at any time by contacting our support team.
                  </p>
                </div>
              </div>

              <div className="mt-4 p-3 bg-white rounded-xl border border-gray-200">
                <div className="flex items-center gap-2 text-xs text-gray-600">
                  <span className="text-gray-500">ℹ️</span>
                  <span>
                    By submitting this application, you consent to the collection and use of your information as described above and in our
                    <button className="text-gray-700 hover:text-gray-900 underline ml-1 font-medium">Privacy Policy</button>.
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
