"use client"

import { useEffect, useState } from "react"
import { use<PERSON>earchP<PERSON><PERSON>, useRout<PERSON> } from "next/navigation"
import Link from "next/link"
import { Building2, Check, ChevronDown, ChevronLeft, ChevronRight, Loader2, Plus, Search, SlidersHorizontal, InfoIcon } from "lucide-react"


import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import { useToast } from "@/components/ui/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

import { propertiesService, PropertyStatus, Property } from "@/lib/services"



// Status display text mapping
const getStatusText = (status: PropertyStatus) => {
  switch (status) {
    case 'LIVE':
      return 'Live';
    case 'RENTED':
      return 'Rented';
    case 'PAUSED':
      return 'Paused';
    default:
      return status;
  }
}

// Get status icon based on status
const getStatusIcon = (status: PropertyStatus) => {
  switch (status) {
    case 'LIVE':
      return <div className="h-2 w-2 rounded-full bg-green-500 mr-1.5" />;
    case 'RENTED':
      return <div className="h-2 w-2 rounded-full bg-blue-500 mr-1.5" />;
    case 'PAUSED':
      return <div className="h-2 w-2 rounded-full bg-red-500 mr-1.5" />;
    default:
      return null;
  }
}

// Get status color classes
const getStatusColorClasses = (status: PropertyStatus) => {
  switch (status) {
    case 'LIVE':
      return 'bg-green-50 text-green-700 border-green-200';
    case 'RENTED':
      return 'bg-blue-50 text-blue-700 border-blue-200';
    case 'PAUSED':
      return 'bg-red-50 text-red-700 border-red-200';
    default:
      return 'bg-gray-50 text-gray-700 border-gray-200';
  }
}

export default function PropertiesPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { toast } = useToast()
  const [properties, setProperties] = useState<Property[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [updatingStatus, setUpdatingStatus] = useState<{id?: string, loading: boolean}>({loading: false})
  const [statusDialogOpen, setStatusDialogOpen] = useState(false)
  const [pendingStatusChange, setPendingStatusChange] = useState<{propertyId?: string, status?: PropertyStatus, propertyTitle?: string}>({})
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(5)

  // Function to handle status change request
  const handleStatusChange = (propertyId: string | undefined, newStatus: PropertyStatus, propertyTitle: string) => {
    setPendingStatusChange({
      propertyId,
      status: newStatus,
      propertyTitle
    })
    setStatusDialogOpen(true)
  }

  // Function to confirm and execute status change
  const confirmStatusChange = async () => {
    if (!pendingStatusChange.propertyId || !pendingStatusChange.status) {
      setStatusDialogOpen(false)
      return
    }
    
    await updatePropertyStatus(pendingStatusChange.propertyId, pendingStatusChange.status)
    setStatusDialogOpen(false)
  }

  // Function to fetch properties from API
  const fetchProperties = async (showLoading = true) => {
    if (showLoading) {
      setLoading(true);
    }
    setError(null);

    try {
      const response = await propertiesService.getProperties();

      if (response.status === 401 || response.status === 403) {
        // Token expired or invalid
        setError("Your session has expired. Please log in again.");
        router.push('/login');
        return;
      }

      if (response.error) {
        throw new Error(response.error);
      }

      setProperties(response.data);
    } catch (error) {
      console.error('Error fetching properties:', error);
      setError("Failed to load properties. Please try again.");
    } finally {
      if (showLoading) {
        setLoading(false);
      }
    }
  };

  // Function to update property status
  const updatePropertyStatus = async (propertyId: string | undefined, newStatus: PropertyStatus) => {
    if (!propertyId) {
      toast({
        title: "Error",
        description: "Property ID is missing",
        variant: "destructive",
      });
      return;
    }

    setUpdatingStatus({id: propertyId, loading: true});

    try {
      const response = await propertiesService.updatePropertyStatus({
        propertyId,
        status: newStatus
      });

      if (response.status === 401 || response.status === 403) {
        // Token expired or invalid
        toast({
          title: "Error",
          description: "Your session has expired. Please log in again.",
          variant: "destructive",
        });
        router.push('/login');
        return;
      }

      if (response.error) {
        // Error toast is already shown by the API service, just return
        return;
      }

      // Refresh the properties list to get updated data from server
      await fetchProperties(false); // Don't show loading spinner since we have status-specific loading

      toast({
        title: "Success",
        description: `Property status updated to ${getStatusText(newStatus)}`,
      });

      // Close the dialog
      setStatusDialogOpen(false);

    } catch (error) {
      console.error('Error updating property status:', error);
      // Don't show error toast here as it's already handled by the API service
      // Only log the error for debugging purposes
    } finally {
      setUpdatingStatus({loading: false});
    }
  };

  // Fetch properties from API on component mount
  useEffect(() => {
    fetchProperties();
  }, [router, toast]);

  // Show success toast when property is added
  useEffect(() => {
    const success = searchParams.get("success")
    if (success === "true") {
      toast({
        title: "Property Added",
        description: "Your property has been successfully added.",
      })
    }
  }, [searchParams, toast])
  
  // Filter properties based on status and search query
  const filteredProperties = properties.filter(property => {
    // Status filter
    if (statusFilter !== 'all' && property.status !== statusFilter) {
      return false
    }
    
    // Search query filter
    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase()
      return (
        (property.title && property.title.toLowerCase().includes(query)) ||
        (property.address && property.address.toLowerCase().includes(query))
      )
    }
    
    return true
  })
  
  // Calculate pagination
  const totalPages = Math.ceil(filteredProperties.length / itemsPerPage)
  const paginatedProperties = filteredProperties.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )
  
  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 rounded-lg bg-purple-600 flex items-center justify-center">
              <Building2 className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Properties</h1>
              <p className="text-gray-600 mt-1">
                Manage and monitor your property portfolio
              </p>
            </div>
          </div>
          <Button asChild className="bg-purple-600 hover:bg-purple-700 text-white">
            <Link href="/dashboard/properties/add">
              <Plus className="h-4 w-4 mr-2" />
              Add Property
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="bg-white border border-gray-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Properties</CardTitle>
            <Building2 className="h-4 w-4 text-gray-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{properties.length}</div>
            <p className="text-xs text-gray-500 mt-1">properties in portfolio</p>
          </CardContent>
        </Card>
        <Card className="bg-white border border-gray-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Live Properties</CardTitle>
            <Check className="h-4 w-4 text-gray-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {properties.filter(p => p.status === 'LIVE').length}
            </div>
            <p className="text-xs text-gray-500 mt-1">currently available</p>
          </CardContent>
        </Card>
        <Card className="bg-white border border-gray-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Rented Properties</CardTitle>
            <Building2 className="h-4 w-4 text-gray-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {properties.filter(p => p.status === 'RENTED').length}
            </div>
            <p className="text-xs text-gray-500 mt-1">currently rented</p>
          </CardContent>
        </Card>
      </div>

      {/* Info Alert */}
      <Alert className="bg-blue-50 border border-blue-200">
        <InfoIcon className="h-4 w-4 text-blue-600" />
        <AlertTitle className="text-blue-900">Important Information</AlertTitle>
        <AlertDescription className="text-blue-800">
          Once a property is marked as <span className="font-semibold">Rented</span>, its status cannot be changed. This ensures accurate record-keeping for rented properties.
        </AlertDescription>
      </Alert>

      {/* Status Change Confirmation Dialog */}
      <AlertDialog open={statusDialogOpen} onOpenChange={setStatusDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change Property Status</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to change the status of <span className="font-medium">{pendingStatusChange.propertyTitle}</span> to <span className="font-medium">{pendingStatusChange.status && getStatusText(pendingStatusChange.status)}</span>?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmStatusChange}
              className={`${
                pendingStatusChange.status === 'LIVE'
                  ? 'bg-green-600 hover:bg-green-700'
                  : pendingStatusChange.status === 'RENTED'
                    ? 'bg-blue-600 hover:bg-blue-700'
                    : 'bg-red-600 hover:bg-red-700'
              }`}
            >
              Confirm
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Properties Table Card */}
      <Card className="bg-white border border-gray-200">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Property Portfolio</CardTitle>
              <CardDescription className="text-gray-500 mt-1">
                Manage and monitor all your properties
              </CardDescription>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mt-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="search"
                placeholder="Search by property name or address..."
                className="pl-10 bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Filters */}
            <div className="flex items-center gap-2">
              <SlidersHorizontal className="h-4 w-4 text-gray-500" />
              <Select
                value={statusFilter}
                onValueChange={setStatusFilter}
              >
                <SelectTrigger className="w-[140px] bg-gray-50 border-gray-200">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Properties</SelectItem>
                  <SelectItem value="LIVE">Live Properties</SelectItem>
                  <SelectItem value="RENTED">Rented Properties</SelectItem>
                  <SelectItem value="PAUSED">Paused Properties</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex flex-col justify-center items-center py-12 gap-4">
              <div className="relative">
                <div className="h-12 w-12 rounded-full bg-purple-100 flex items-center justify-center">
                  <Building2 className="h-6 w-6 text-purple-600" />
                </div>
                <div className="absolute inset-0 rounded-full border-2 border-purple-600 border-t-transparent animate-spin"></div>
              </div>
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-1">Loading Properties</h3>
                <p className="text-gray-600 text-sm">Fetching your property portfolio...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
                <Building2 className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Properties</h3>
              <p className="text-gray-600 mb-4 text-sm">{error}</p>
              <Button
                onClick={() => fetchProperties()}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                Try Again
              </Button>
            </div>
          ) : (
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="font-medium text-gray-900">Property Details</TableHead>
                    <TableHead className="hidden md:table-cell font-medium text-gray-900">Monthly Rent</TableHead>
                    <TableHead className="text-right font-medium text-gray-900">Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedProperties.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-12">
                        <div className="flex flex-col items-center justify-center gap-4">
                          <div className="h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center">
                            <Building2 className="h-6 w-6 text-gray-400" />
                          </div>
                          <div className="text-center">
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Properties Found</h3>
                            <p className="text-gray-600 text-sm mb-4">
                              {searchQuery || statusFilter !== 'all'
                                ? "No properties match your current search criteria."
                                : "You haven't added any properties yet."
                              }
                            </p>
                            {searchQuery || statusFilter !== 'all' ? (
                              <Button
                                variant="outline"
                                onClick={() => {
                                  setSearchQuery('')
                                  setStatusFilter('all')
                                }}
                                className="border-purple-200 text-purple-600 hover:bg-purple-50"
                              >
                                Clear Filters
                              </Button>
                            ) : (
                              <Button asChild className="bg-purple-600 hover:bg-purple-700 text-white">
                                <Link href="/dashboard/properties/add">
                                  <Plus className="h-4 w-4 mr-2" />
                                  Add Your First Property
                                </Link>
                              </Button>
                            )}
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    paginatedProperties.map((property, index) => (
                      <TableRow
                        key={property.id || index}
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => router.push(`/dashboard/properties/${property.id}`)}
                      >
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="relative">
                              <div className="h-10 w-10 rounded-lg bg-purple-100 flex items-center justify-center">
                                <Building2 className="h-5 w-5 text-purple-600" />
                              </div>
                              <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full ${
                                property.status === 'LIVE' ? 'bg-green-500' :
                                property.status === 'RENTED' ? 'bg-blue-500' : 'bg-red-500'
                              } border-2 border-white`}></div>
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">
                                {property.title}
                              </div>
                              <div className="text-sm text-gray-500">{property.address}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          <div className="text-right">
                            <div className="font-medium text-gray-900">
                              {property.price ? new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: 'USD',
                                maximumFractionDigits: 0
                              }).format(property.price) : 'N/A'}
                            </div>
                            <div className="text-xs text-gray-500">per month</div>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            {property.status === 'RENTED' ? (
                              <div className={`flex items-center px-2 py-1 rounded-lg text-xs font-medium border ${getStatusColorClasses(property.status)}`}>
                                {getStatusIcon(property.status)}
                                {getStatusText(property.status)}
                              </div>
                            ) : (
                              <div className="flex items-center rounded-lg overflow-hidden border border-gray-200">
                                <button
                                  className={`flex items-center px-2 py-1 text-xs font-medium ${getStatusColorClasses(property.status)}`}
                                  disabled={updatingStatus.loading}
                                >
                                  {getStatusIcon(property.status)}
                                  {getStatusText(property.status)}
                                </button>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <button
                                      className={`px-2 py-1 border-l ${
                                        property.status === 'LIVE'
                                          ? 'bg-green-50 border-green-200 text-green-700 hover:bg-green-100'
                                          : property.status === 'PAUSED'
                                            ? 'bg-red-50 border-red-200 text-red-700 hover:bg-red-100'
                                            : 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
                                      }`}
                                      disabled={updatingStatus.loading}
                                      onClick={(e) => e.stopPropagation()}
                                    >
                                      {updatingStatus.id === property.id && updatingStatus.loading ? (
                                        <Loader2 className="h-3 w-3 animate-spin" />
                                      ) : (
                                        <ChevronDown className="h-3 w-3" />
                                      )}
                                    </button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end" className="w-48">
                                    <DropdownMenuLabel className="text-xs font-medium">Change Status</DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    {property.status !== 'LIVE' && (
                                      <DropdownMenuItem
                                        className="text-xs flex items-center gap-2"
                                        disabled={updatingStatus.loading}
                                        onClick={(e) => {
                                          e.stopPropagation()
                                          handleStatusChange(property.id, 'LIVE', property.title)
                                        }}
                                      >
                                        <div className="h-2 w-2 rounded-full bg-green-500" />
                                        Make Live
                                      </DropdownMenuItem>
                                    )}
                                    <DropdownMenuItem
                                      className="text-xs flex items-center gap-2"
                                      disabled={updatingStatus.loading}
                                      onClick={(e) => {
                                        e.stopPropagation()
                                        handleStatusChange(property.id, 'RENTED', property.title)
                                      }}
                                    >
                                      <div className="h-2 w-2 rounded-full bg-blue-500" />
                                      Mark as Rented
                                    </DropdownMenuItem>
                                    {property.status !== 'PAUSED' && (
                                      <DropdownMenuItem
                                        className="text-xs flex items-center gap-2"
                                        disabled={updatingStatus.loading}
                                        onClick={(e) => {
                                          e.stopPropagation()
                                          handleStatusChange(property.id, 'PAUSED', property.title)
                                        }}
                                      >
                                        <div className="h-2 w-2 rounded-full bg-red-500" />
                                        Pause Property
                                      </DropdownMenuItem>
                                    )}
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>

        {/* Pagination */}
        {filteredProperties.length > 0 && (
          <CardFooter className="flex items-center justify-between border-t border-gray-200 px-6 py-4">
            <div className="text-sm text-gray-600">
              Showing {Math.min((currentPage - 1) * itemsPerPage + 1, filteredProperties.length)} to{" "}
              {Math.min(currentPage * itemsPerPage, filteredProperties.length)} of{" "}
              {filteredProperties.length} properties
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Show</span>
                <Select
                  value={itemsPerPage.toString()}
                  onValueChange={(value) => {
                    setItemsPerPage(Number(value))
                    setCurrentPage(1)
                  }}
                >
                  <SelectTrigger className="h-8 w-16 bg-gray-50 border-gray-200">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-3 w-3" />
                </Button>
                {Array.from({ length: totalPages }, (_, i) => i + 1)
                  .filter(page => {
                    return page === 1 ||
                           page === totalPages ||
                           Math.abs(page - currentPage) <= 1
                  })
                  .map((page, index, array) => {
                    const showEllipsisBefore = index > 0 && page - array[index - 1] > 1

                    return (
                      <div key={page} className="flex items-center">
                        {showEllipsisBefore && (
                          <span className="px-2 text-gray-400">...</span>
                        )}
                        <Button
                          variant={currentPage === page ? "default" : "outline"}
                          size="icon"
                          className={`h-8 w-8 ${
                            currentPage === page
                              ? 'bg-purple-600 hover:bg-purple-700 text-white'
                              : ''
                          }`}
                          onClick={() => handlePageChange(page)}
                        >
                          {page}
                        </Button>
                      </div>
                    )
                  })}
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </CardFooter>
        )}
      </Card>
    </div>
  )
}