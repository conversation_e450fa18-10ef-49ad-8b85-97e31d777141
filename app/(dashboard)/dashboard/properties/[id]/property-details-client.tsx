"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import {
  ArrowLeft,
  Building2,
  MapPin,
  Bed,
  Bath,
  DollarSign,
  Calendar,
  Eye,
  FileText,
  Shield,
  Check,
  Loader2,
  Share2,
  Heart,
  Home,
  Cigarette,
  PawPrint,
  Sofa,
  ChevronLeft,
  ChevronRight
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { propertiesService } from "@/lib/services"
import type { PropertyDetails } from "@/lib/types"



export default function PropertyDetailsClient() {
  const params = useParams()
  const router = useRouter()
  const [property, setProperty] = useState<PropertyDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  const propertyId = params.id as string

  // Handle case where propertyId might not be available immediately (static export)
  if (!propertyId) {
    return (
      <div className="flex flex-col justify-center items-center py-16 gap-3">
        <div className="relative">
          <div className="h-16 w-16 rounded-full bg-gray-100 flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          </div>
          <div className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-white flex items-center justify-center shadow-md">
            <Building2 className="h-3.5 w-3.5 text-blue-600" />
          </div>
        </div>
        <span className="text-gray-600 font-medium">Loading property...</span>
      </div>
    )
  }

  // Function to fetch property details from API
  const fetchPropertyDetails = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await propertiesService.getPropertyById(propertyId)

      if (response.status === 401 || response.status === 403) {
        setError("Your session has expired. Please log in again.")
        router.push('/login')
        return
      }

      if (response.status === 404) {
        setError("Property not found.")
        return
      }

      if (response.error) {
        throw new Error(response.error)
      }

      setProperty(response.data)
    } catch (error) {
      console.error('Error fetching property details:', error)
      setError("Failed to load property details. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  // Fetch property details on component mount
  useEffect(() => {
    if (propertyId) {
      fetchPropertyDetails()
    }
  }, [propertyId, router])

  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center py-16 gap-3">
        <div className="relative">
          <div className="h-16 w-16 rounded-full bg-gray-100 flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          </div>
          <div className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-white flex items-center justify-center shadow-md">
            <Building2 className="h-3.5 w-3.5 text-blue-600" />
          </div>
        </div>
        <span className="text-gray-600 font-medium">Loading property details...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/properties">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Properties
            </Link>
          </Button>
        </div>
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!property) {
    return (
      <div className="space-y-8">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/properties">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Properties
            </Link>
          </Button>
        </div>
        <Alert>
          <AlertDescription>Property not found.</AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" asChild>
              <Link href="/dashboard/properties">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Properties
              </Link>
            </Button>
            <div className="h-10 w-10 rounded-lg bg-purple-600 flex items-center justify-center">
              <Building2 className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">{property.title}</h1>
              <div className="flex items-center gap-2 text-gray-600 mt-1">
                <MapPin className="h-4 w-4" />
                <span>{property.address}</span>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                maximumFractionDigits: 0
              }).format(property.price)}
            </div>
            <div className="text-gray-500 text-sm">/month</div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-3">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <Eye className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-lg font-semibold text-gray-900">{property.numberOfVisits}</div>
                <div className="text-sm text-gray-500">Total Visits</div>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <FileText className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-lg font-semibold text-gray-900">{property.numberOfApplications}</div>
                <div className="text-sm text-gray-500">Applications</div>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <Heart className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {property.numberOfVisits > 0 ? Math.round((property.numberOfApplications / property.numberOfVisits) * 100) : 0}%
                </div>
                <div className="text-sm text-gray-500">Interest Rate</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
        {/* Left Column - Images and Details */}
        <div className="xl:col-span-2 space-y-6">
          {/* Image Gallery */}
          {property.images && property.images.length > 0 ? (
            <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
              <div className="relative aspect-video group">
                <Image
                  src={property.images[currentImageIndex]}
                  alt={`${property.title} - Image ${currentImageIndex + 1}`}
                  fill
                  className="object-cover"
                />

                {/* Image Navigation Arrows */}
                {property.images.length > 1 && (
                  <>
                    <button
                      onClick={() => setCurrentImageIndex(currentImageIndex > 0 ? currentImageIndex - 1 : property.images.length - 1)}
                      className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <ChevronLeft className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => setCurrentImageIndex(currentImageIndex < property.images.length - 1 ? currentImageIndex + 1 : 0)}
                      className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <ChevronRight className="h-5 w-5" />
                    </button>
                  </>
                )}

                {/* Image Counter */}
                <div className="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                  {currentImageIndex + 1} / {property.images.length}
                </div>

                {/* Dot Indicators */}
                {property.images.length > 1 && property.images.length <= 8 && (
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                    {property.images.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`w-3 h-3 rounded-full transition-all ${
                          index === currentImageIndex
                            ? 'bg-white'
                            : 'bg-white/50 hover:bg-white/75'
                        }`}
                      />
                    ))}
                  </div>
                )}
              </div>

              {/* Thumbnail Gallery */}
              {property.images.length > 1 && (
                <div className="p-4 bg-gray-50">
                  <div className="flex gap-3 overflow-x-auto">
                    {property.images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`relative w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 border-2 transition-all ${
                          index === currentImageIndex
                            ? 'border-purple-500'
                            : 'border-gray-200 hover:border-purple-300'
                        }`}
                      >
                        <Image
                          src={image}
                          alt={`Thumbnail ${index + 1}`}
                          fill
                          className="object-cover"
                        />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="aspect-video flex items-center justify-center bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg">
              <div className="text-center p-4">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Building2 className="h-8 w-8 text-gray-400" />
                </div>
                <p className="text-gray-500 font-medium">No images available</p>
                <p className="text-gray-400 text-sm mt-1">Images will be displayed here once uploaded</p>
              </div>
            </div>
          )}

          {/* Property Description */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Property Description</h2>
            <p className="text-gray-600 leading-relaxed">
              {property.description || "No description available for this property."}
            </p>
          </div>

          {/* Property Features */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Property Features</h2>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Bed className="h-6 w-6 text-white" />
                </div>
                <p className="text-sm text-gray-500">Bedrooms</p>
                <p className="text-xl font-semibold text-gray-900">{property.bedroomNumber}</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Bath className="h-6 w-6 text-white" />
                </div>
                <p className="text-sm text-gray-500">Bathrooms</p>
                <p className="text-xl font-semibold text-gray-900">{property.bathroomNumber}</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Home className="h-6 w-6 text-white" />
                </div>
                <p className="text-sm text-gray-500">Type</p>
                <p className="text-lg font-semibold text-gray-900">{property.type}</p>
              </div>

              <div className="text-center">
                <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <DollarSign className="h-6 w-6 text-white" />
                </div>
                <p className="text-sm text-gray-500">Monthly Rent</p>
                <p className="text-lg font-semibold text-gray-900">
                  {new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    maximumFractionDigits: 0
                  }).format(property.price)}
                </p>
              </div>
            </div>
          </div>

          {/* Amenities & Features */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Amenities & Features</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {/* Pet Friendly */}
              <div className="flex items-center gap-3">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                  property.isPetFriendly ? 'bg-purple-600' : 'bg-gray-300'
                }`}>
                  <PawPrint className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Pet Friendly</p>
                  <p className={`text-sm ${
                    property.isPetFriendly ? 'text-purple-600' : 'text-gray-500'
                  }`}>
                    {property.isPetFriendly ? 'Pets Welcome' : 'No Pets Allowed'}
                  </p>
                </div>
                {property.isPetFriendly && (
                  <div className="ml-auto">
                    <Check className="h-4 w-4 text-purple-600" />
                  </div>
                )}
              </div>

              {/* Smoking Policy */}
              <div className="flex items-center gap-3">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                  property.isSmokingAllowed ? 'bg-purple-600' : 'bg-gray-300'
                }`}>
                  <Cigarette className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Smoking Policy</p>
                  <p className={`text-sm ${
                    property.isSmokingAllowed ? 'text-purple-600' : 'text-gray-500'
                  }`}>
                    {property.isSmokingAllowed ? 'Smoking Allowed' : 'No Smoking'}
                  </p>
                </div>
                {property.isSmokingAllowed && (
                  <div className="ml-auto">
                    <Check className="h-4 w-4 text-purple-600" />
                  </div>
                )}
              </div>

              {/* Furnished Status */}
              <div className="flex items-center gap-3">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                  property.isFurnished || property.isSemiFurnished ? 'bg-purple-600' : 'bg-gray-300'
                }`}>
                  <Sofa className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Furnishing</p>
                  <p className={`text-sm ${
                    property.isFurnished || property.isSemiFurnished ? 'text-purple-600' : 'text-gray-500'
                  }`}>
                    {property.isFurnished ? 'Fully Furnished' : property.isSemiFurnished ? 'Semi-Furnished' : 'Unfurnished'}
                  </p>
                </div>
                {(property.isFurnished || property.isSemiFurnished) && (
                  <div className="ml-auto">
                    <Check className="h-4 w-4 text-purple-600" />
                  </div>
                )}
              </div>

              {/* Availability */}
              <div className="flex items-center gap-3">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                  property.isFreeNow ? 'bg-purple-600' : 'bg-gray-300'
                }`}>
                  <Calendar className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Availability</p>
                  <p className={`text-sm ${
                    property.isFreeNow ? 'text-purple-600' : 'text-gray-500'
                  }`}>
                    {property.isFreeNow ? 'Available Now' : 'Available Soon'}
                  </p>
                  {property.startDate && !property.isFreeNow && (
                    <p className="text-xs text-gray-500 mt-1">
                      From {new Date(property.startDate).toLocaleDateString()}
                    </p>
                  )}
                </div>
                <div className="ml-auto">
                  <Check className={`h-4 w-4 ${property.isFreeNow ? 'text-purple-600' : 'text-gray-500'}`} />
                </div>
              </div>
            </div>
          </div>

          {/* Required Verifications */}
          {property.requiredVerifications && property.requiredVerifications.length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="mb-4">
                <h2 className="text-lg font-semibold text-gray-900">Required Verifications</h2>
                <p className="text-gray-500 text-sm mt-1">Documents and verifications needed for application</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {property.requiredVerifications.map((verification, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                      <Shield className="h-4 w-4 text-white" />
                    </div>
                    <span className="font-medium text-gray-900 flex-1">{verification}</span>
                    <Check className="h-4 w-4 text-purple-600" />
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Right Column - Quick Actions */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
            <div className="space-y-3">
              <Button
                className="w-full bg-purple-600 hover:bg-purple-700 text-white flex items-center justify-center relative"
                onClick={() => router.push(`/dashboard/properties/${propertyId}/candidates`)}
              >
                <Eye className="h-4 w-4 mr-2" />
                View Applications
                <span className="absolute right-3 bg-white/20 px-2 py-0.5 rounded-full text-xs">
                  {property.numberOfApplications}
                </span>
              </Button>

              <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white">
                <FileText className="h-4 w-4 mr-2" />
                Generate Report
              </Button>

              <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white">
                <Share2 className="h-4 w-4 mr-2" />
                Share Property
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
