"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  ArrowLeft,
  Search,
  User,
  Mail,
  Phone,
  Calendar,
  Star,
  CheckCircle,
  Clock,
  X,
  AlertCircle,
  Users,
  Filter,
  Eye
} from "lucide-react"
import { propertiesService } from "@/lib/services"
import { useToast } from "@/components/ui/use-toast"

// Types based on the API response
interface Candidate {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  earliestMoveInDate: string // LocalDate from backend
  latestMoveInDate: string // LocalDate from backend
  rank: number
  vestralScore: number
  status: 'IN_PROGRESS' | 'COMPLETED'
  isCancelledByCandidate: boolean
  isCancelledByManager: boolean
  cancellationReason?: string
  currentStepMessage?: string
}

export default function PropertyCandidatesPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const propertyId = params.id as string

  const [candidates, setCandidates] = useState<Candidate[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<'all' | 'IN_PROGRESS' | 'COMPLETED'>('all')

  // Fetch candidates
  useEffect(() => {
    const fetchCandidates = async () => {
      if (!propertyId) return

      try {
        setLoading(true)
        setError(null)
        
        const response = await propertiesService.getPropertyCandidates(propertyId)

        if (response.error) {
          setError(response.error)
          toast({
            title: "Error",
            description: response.error,
            variant: "destructive",
          })
        } else {
          setCandidates((response.data as Candidate[]) || [])
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to fetch candidates"
        setError(errorMessage)
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchCandidates()
  }, [propertyId, toast])

  // Filter candidates
  const filteredCandidates = candidates.filter(candidate => {
    const matchesSearch = 
      candidate.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      candidate.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      candidate.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      candidate.phone.includes(searchQuery)

    const matchesStatus = statusFilter === 'all' || candidate.status === statusFilter

    return matchesSearch && matchesStatus
  })

  // Get status styling
  const getStatusStyling = (candidate: Candidate) => {
    if (candidate.isCancelledByCandidate || candidate.isCancelledByManager) {
      return {
        badge: "bg-red-100 text-red-800 border-red-200",
        text: "Cancelled",
        icon: X
      }
    }

    switch (candidate.status) {
      case 'COMPLETED':
        return {
          badge: "bg-green-100 text-green-800 border-green-200",
          text: "Completed",
          icon: CheckCircle
        }
      case 'IN_PROGRESS':
        return {
          badge: "bg-blue-100 text-blue-800 border-blue-200",
          text: "In Progress",
          icon: Clock
        }
      default:
        return {
          badge: "bg-gray-100 text-gray-800 border-gray-200",
          text: "Unknown",
          icon: AlertCircle
        }
    }
  }

  // Get score color
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600 bg-green-50"
    if (score >= 60) return "text-yellow-600 bg-yellow-50"
    return "text-red-600 bg-red-50"
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="h-10 w-10 rounded-lg bg-purple-600 flex items-center justify-center">
              <Users className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Property Candidates</h1>
              <p className="text-gray-600 mt-1">
                {loading ? 'Loading candidates...' : `${candidates.length} ${candidates.length === 1 ? 'candidate' : 'candidates'} found`}
              </p>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-3">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <Users className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-lg font-semibold text-gray-900">{candidates.length}</div>
                <div className="text-sm text-gray-500">Total Applications</div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <CheckCircle className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {candidates.filter(c => c.status === 'COMPLETED').length}
                </div>
                <div className="text-sm text-gray-500">Completed</div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <Clock className="h-5 w-5 text-gray-400" />
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {candidates.filter(c => c.status === 'IN_PROGRESS').length}
                </div>
                <div className="text-sm text-gray-500">In Progress</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by name, email, or phone..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          {/* Status Filter */}
          <div className="flex items-center gap-3">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as typeof statusFilter)}
              className="px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm min-w-[140px]"
            >
              <option value="all">All Status</option>
              <option value="IN_PROGRESS">In Progress</option>
              <option value="COMPLETED">Completed</option>
            </select>
          </div>

          {/* Quick Stats */}
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>{candidates.filter(c => c.status === 'COMPLETED').length} Completed</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>{candidates.filter(c => c.status === 'IN_PROGRESS').length} In Progress</span>
            </div>
          </div>
        </div>
      </div>

      {/* Candidates List */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <Users className="h-5 w-5 text-purple-600" />
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Candidate Applications</h2>
              <p className="text-gray-600 text-sm mt-1">
                Review and manage property applications with detailed candidate information
              </p>
            </div>
          </div>
        </div>
        <div className="p-6">
          {loading ? (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-gray-400 animate-pulse" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Candidates</h3>
              <p className="text-gray-500">Fetching application data...</p>
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Error Loading Candidates</h3>
              <p className="text-gray-600 mb-6 max-w-md">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                <AlertCircle className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          ) : filteredCandidates.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">No Candidates Found</h3>
              <p className="text-gray-600 max-w-md">
                {searchQuery || statusFilter !== 'all'
                  ? "No candidates match your current search criteria. Try adjusting your filters or search terms."
                  : "No candidates have applied for this property yet. Applications will appear here once submitted."
                }
              </p>
              {(searchQuery || statusFilter !== 'all') && (
                <Button
                  onClick={() => {
                    setSearchQuery("")
                    setStatusFilter('all')
                  }}
                  variant="outline"
                  className="mt-4 border-purple-200 text-purple-600 hover:bg-purple-50"
                >
                  Clear Filters
                </Button>
              )}
            </div>
          ) : (
            <ScrollArea className="h-[600px]">
              <div className="space-y-4">
                {filteredCandidates.map((candidate) => {
                  const statusInfo = getStatusStyling(candidate)
                  const StatusIcon = statusInfo.icon

                  return (
                    <div key={candidate.id} className="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6 mb-6">
                        <div className="flex items-center gap-4">
                          <div className="relative">
                            <div className="h-12 w-12 bg-purple-600 rounded-lg flex items-center justify-center">
                              <User className="h-6 w-6 text-white" />
                            </div>
                            <div className="absolute -top-2 -right-2 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center text-xs font-bold text-white">
                              #{candidate.rank}
                            </div>
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg text-gray-900 mb-2">
                              {candidate.firstName} {candidate.lastName}
                            </h3>
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                              <div className="flex items-center gap-2 text-gray-600">
                                <Mail className="h-4 w-4 text-blue-600" />
                                <span className="text-sm">{candidate.email}</span>
                              </div>
                              <div className="flex items-center gap-2 text-gray-600">
                                <Phone className="h-4 w-4 text-green-600" />
                                <span className="text-sm">{candidate.phone}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                          {/* Vestral Score */}
                          <div className={`px-4 py-2 rounded-lg ${getScoreColor(candidate.vestralScore)}`}>
                            <div className="flex items-center gap-2">
                              <Star className="h-4 w-4" />
                              <div>
                                <div className="text-xs font-medium opacity-80">Vestral Score</div>
                                <div className="text-lg font-bold">{candidate.vestralScore}</div>
                              </div>
                            </div>
                          </div>

                          {/* Status */}
                          <Badge className={`${statusInfo.badge} px-3 py-1 text-sm font-medium flex items-center gap-2`}>
                            <StatusIcon className="h-4 w-4" />
                            {statusInfo.text}
                          </Badge>
                        </div>
                      </div>

                      {/* Move-in Dates */}
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                          <div className="flex items-center gap-3">
                            <Calendar className="h-4 w-4 text-blue-600" />
                            <div>
                              <p className="text-xs font-medium text-blue-600 mb-1">Earliest Move-in</p>
                              <p className="text-sm font-semibold text-blue-900">{formatDate(candidate.earliestMoveInDate)}</p>
                            </div>
                          </div>
                        </div>
                        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                          <div className="flex items-center gap-3">
                            <Calendar className="h-4 w-4 text-green-600" />
                            <div>
                              <p className="text-xs font-medium text-green-600 mb-1">Latest Move-in</p>
                              <p className="text-sm font-semibold text-green-900">{formatDate(candidate.latestMoveInDate)}</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Current Step Message */}
                      {candidate.currentStepMessage && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                          <div className="flex items-start gap-3">
                            <Clock className="h-4 w-4 text-blue-600 mt-0.5" />
                            <div>
                              <h4 className="font-medium text-blue-900 mb-1">Current Step</h4>
                              <p className="text-blue-800 text-sm">{candidate.currentStepMessage}</p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Cancellation Reason */}
                      {(candidate.isCancelledByCandidate || candidate.isCancelledByManager) && candidate.cancellationReason && (
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                          <div className="flex items-start gap-3">
                            <X className="h-4 w-4 text-red-600 mt-0.5" />
                            <div>
                              <h4 className="font-medium text-red-900 mb-1">
                                Cancelled by {candidate.isCancelledByCandidate ? 'Candidate' : 'Manager'}
                              </h4>
                              <p className="text-red-800 text-sm">{candidate.cancellationReason}</p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex flex-col sm:flex-row justify-end gap-3 pt-4 border-t border-gray-200">
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-purple-200 text-purple-600 hover:bg-purple-50"
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                        <Button
                          size="sm"
                          className="bg-purple-600 hover:bg-purple-700 text-white"
                        >
                          <Mail className="h-4 w-4 mr-2" />
                          Contact
                        </Button>
                      </div>
                    </div>
                  )
                })}
              </div>
            </ScrollArea>
          )}
        </div>
      </div>
    </div>
  )
}
