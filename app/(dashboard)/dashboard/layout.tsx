"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import {
  Menu as MenuIcon,
  Bell,
  Search,
  X,
  ChevronLeft,
  ChevronRight
} from "lucide-react"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { logout } from "@/lib/auth.client"
import { AuthHandler } from "./auth-handler"
import { Sidebar } from "@/components/dashboard/sidebar"
import { ProtectedRoute } from "@/components/auth/ProtectedRoute"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const pathname = usePathname();

  // Toggle sidebar collapsed state
  const toggleSidebarCollapse = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  }

  // Close mobile sidebar when route changes
  useEffect(() => {
    setIsSidebarOpen(false);
  }, [pathname]);

  // Load sidebar collapsed state from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedState = localStorage.getItem('sidebarCollapsed');
      if (savedState) {
        setIsSidebarCollapsed(savedState === 'true');
      }
    }
  }, []);

  // Save sidebar collapsed state to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('sidebarCollapsed', isSidebarCollapsed.toString());
    }
  }, [isSidebarCollapsed]);

  return (
    <ProtectedRoute>
      <div className="flex min-h-screen bg-gray-50">
        {/* Auth Handler - processes tokens from implicit flow */}
        <AuthHandler />

      {/* Mobile Sidebar */}
      <Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden absolute top-4 left-4 z-50 bg-white shadow-sm border border-gray-200"
          >
            <MenuIcon className="h-5 w-5 text-gray-600" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="p-0 w-64 bg-white border-r border-gray-200">
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="h-8 w-8 rounded-lg bg-purple-600 flex items-center justify-center">
                <span className="text-sm font-bold text-white">V</span>
              </div>
              <div>
                <span className="text-lg font-semibold text-gray-900">Vestral</span>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => setIsSidebarOpen(false)}
            >
              <X className="h-4 w-4 text-gray-500" />
            </Button>
          </div>
          <ScrollArea className="h-[calc(100vh-64px)]">
            <Sidebar />
          </ScrollArea>
        </SheetContent>
      </Sheet>

      {/* Desktop Sidebar - clean minimal design */}
      <div className={cn(
        "hidden lg:block fixed inset-y-0 left-0 z-30 bg-white border-r border-gray-200",
        isSidebarCollapsed ? "w-16" : "w-64"
      )}>
        <div className="flex h-16 items-center justify-between px-4 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 rounded-lg bg-purple-600 flex items-center justify-center">
              <span className="text-sm font-bold text-white">V</span>
            </div>
            {!isSidebarCollapsed && (
              <div>
                <span className="text-lg font-semibold text-gray-900">Vestral</span>
              </div>
            )}
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 hover:bg-gray-100"
            onClick={toggleSidebarCollapse}
          >
            {isSidebarCollapsed ? (
              <ChevronRight className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronLeft className="h-4 w-4 text-gray-500" />
            )}
          </Button>
        </div>
        <ScrollArea className="h-[calc(100vh-64px)]">
          <Sidebar collapsed={isSidebarCollapsed} />
        </ScrollArea>
      </div>

      {/* Main Content - responsive padding based on sidebar state */}
      <div className={cn(
        "flex-1",
        isSidebarCollapsed ? "lg:ml-16" : "lg:ml-64"
      )}>
        {/* Clean Header */}
        <header className="bg-white border-b border-gray-200">
          <div className="flex h-16 items-center justify-between px-6">
            {/* Left Section - Search */}
            <div className="flex items-center gap-4 flex-1 max-w-md">
              {/* Mobile Menu Button space */}
              <div className="w-10 h-10 lg:hidden"></div>

              {/* Search */}
              <div className="relative w-full">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="search"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search..."
                  className="w-full bg-gray-50 pl-10 pr-4 py-2 text-sm rounded-lg border border-gray-200
                           focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent
                           placeholder:text-gray-400"
                />
              </div>
            </div>

            {/* Right Section - Actions & Profile */}
            <div className="flex items-center gap-3">
              {/* Notifications */}
              <Button
                variant="ghost"
                size="icon"
                className="relative h-9 w-9 hover:bg-gray-100"
              >
                <Bell className="h-4 w-4 text-gray-500" />
                <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-xs font-medium text-white">3</span>
                </span>
              </Button>

              {/* User Profile */}
              <Button
                variant="ghost"
                size="icon"
                className="h-9 w-9 hover:bg-gray-100"
                onClick={() => logout()}
              >
                <div className="h-7 w-7 rounded-full bg-purple-600 flex items-center justify-center">
                  <span className="text-xs font-medium text-white">IE</span>
                </div>
              </Button>
            </div>
          </div>
        </header>

        {/* Main content */}
        <main className="p-6 bg-gray-50 min-h-[calc(100vh-4rem)]">
          <div className="mx-auto max-w-7xl">
            {children}
          </div>
        </main>
      </div>
    </div>
    </ProtectedRoute>
  )
}