"use client"

import { useState } from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  HelpCircle,
  MessageCircle,
  FileText,
  Video,
  Mail,
  Phone,
  Clock,
  CheckCircle,
  Search,
  ExternalLink,
  BookOpen,
  Users,
  Zap,
  Shield
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export default function SupportPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [contactForm, setContactForm] = useState({
    subject: "",
    message: "",
    priority: "medium"
  })

  const { toast } = useToast()

  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    toast({
      title: "Support Request Sent",
      description: "We'll get back to you within 24 hours.",
      className: "bg-green-50 border-green-200 text-green-800",
    })
    setContactForm({ subject: "", message: "", priority: "medium" })
  }

  const faqItems = [
    {
      question: "How do I add a new property?",
      answer: "Navigate to Properties > Add Property. Fill in the required details including title, address, price, and upload photos. The property will be reviewed before going live.",
      category: "Properties"
    },
    {
      question: "How do I manage tenant applications?",
      answer: "Go to Tenants section to view all applications. You can approve, reject, or request additional information from potential tenants.",
      category: "Tenants"
    },
    {
      question: "How do I update property status?",
      answer: "In the Properties list, click the status dropdown next to any property to change it between Live, Rented, or Paused.",
      category: "Properties"
    },
    {
      question: "How do I generate reports?",
      answer: "Visit the Accounting section to generate financial reports, rent rolls, and property performance analytics.",
      category: "Reports"
    },
    {
      question: "How do I handle maintenance requests?",
      answer: "The Maintenance section allows you to track, assign, and manage all property maintenance requests from tenants.",
      category: "Maintenance"
    }
  ]

  const quickActions = [
    {
      title: "Getting Started Guide",
      description: "Complete walkthrough for new users",
      icon: BookOpen,
      href: "#",
      gradient: "from-blue-500 to-blue-600",
      bgColor: "bg-gradient-to-br from-blue-50 to-blue-100/50",
      iconColor: "text-blue-600"
    },
    {
      title: "Video Tutorials",
      description: "Step-by-step video guides",
      icon: Video,
      href: "#",
      gradient: "from-purple-500 to-purple-600",
      bgColor: "bg-gradient-to-br from-purple-50 to-purple-100/50",
      iconColor: "text-purple-600"
    },
    {
      title: "Community Forum",
      description: "Connect with other property managers",
      icon: Users,
      href: "#",
      gradient: "from-green-500 to-green-600",
      bgColor: "bg-gradient-to-br from-green-50 to-green-100/50",
      iconColor: "text-green-600"
    },
    {
      title: "Feature Requests",
      description: "Suggest new features",
      icon: Zap,
      href: "#",
      gradient: "from-orange-500 to-orange-600",
      bgColor: "bg-gradient-to-br from-orange-50 to-orange-100/50",
      iconColor: "text-orange-600"
    }
  ]

  const filteredFAQ = faqItems.filter(item => 
    item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
    item.category.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center gap-4 mb-4">
          <div className="h-10 w-10 rounded-lg bg-purple-600 flex items-center justify-center">
            <HelpCircle className="h-5 w-5 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Help & Support</h1>
            <p className="text-gray-600 mt-1">
              Get help, find answers, and connect with our support team
            </p>
          </div>
        </div>
        <div className="flex items-center gap-6 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            <span>24/7 Support Available</span>
          </div>
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            <span>Average Response: 2 hours</span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {quickActions.map((action, index) => (
          <div key={index} className="bg-white rounded-lg border border-gray-200 p-6 hover:bg-gray-50 cursor-pointer">
            <div className="space-y-4">
              <div className="p-3 rounded-lg bg-gray-100">
                <action.icon className={`h-6 w-6 ${action.iconColor}`} />
              </div>
              <div className="space-y-2">
                <h3 className="font-semibold text-gray-900">
                  {action.title}
                </h3>
                <p className="text-sm text-gray-600">{action.description}</p>
              </div>
              <div className="flex items-center justify-end pt-2">
                <ExternalLink className="h-4 w-4 text-gray-400" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content */}
      <Tabs defaultValue="faq" className="space-y-6">
        <div className="flex justify-center">
          <TabsList className="grid w-full max-w-md grid-cols-3 bg-gray-100 p-1 rounded-lg">
            <TabsTrigger
              value="faq"
              className="flex items-center gap-2 rounded-md data-[state=active]:bg-white data-[state=active]:text-purple-600"
            >
              <HelpCircle className="h-4 w-4" />
              <span className="hidden sm:inline">FAQ</span>
            </TabsTrigger>
            <TabsTrigger
              value="contact"
              className="flex items-center gap-2 rounded-md data-[state=active]:bg-white data-[state=active]:text-purple-600"
            >
              <MessageCircle className="h-4 w-4" />
              <span className="hidden sm:inline">Contact</span>
            </TabsTrigger>
            <TabsTrigger
              value="resources"
              className="flex items-center gap-2 rounded-md data-[state=active]:bg-white data-[state=active]:text-purple-600"
            >
              <FileText className="h-4 w-4" />
              <span className="hidden sm:inline">Resources</span>
            </TabsTrigger>
          </TabsList>
        </div>

        {/* FAQ Tab */}
        <TabsContent value="faq" className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <HelpCircle className="h-5 w-5 text-purple-600" />
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">Frequently Asked Questions</h2>
                  <p className="text-gray-600 text-sm mt-1">
                    Find quick answers to common questions about Vestral Property Manager
                  </p>
                </div>
              </div>
            </div>
            <div className="p-6 space-y-6">
              {/* Search FAQ */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search FAQ..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>

              {/* FAQ Items */}
              <div className="space-y-4">
                {filteredFAQ.map((item, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                    <div className="flex items-start justify-between gap-4 mb-3">
                      <h3 className="font-semibold text-gray-900">
                        {item.question}
                      </h3>
                      <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200 text-xs">
                        {item.category}
                      </Badge>
                    </div>
                    <p className="text-gray-600">{item.answer}</p>
                  </div>
                ))}
              </div>

              {filteredFAQ.length === 0 && (
                <div className="text-center py-12">
                  <HelpCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-700 mb-2">No FAQ items found</h3>
                  <p className="text-gray-500">Try adjusting your search terms or browse all categories.</p>
                </div>
              )}
            </div>
          </div>
        </TabsContent>

        {/* Contact Support Tab */}
        <TabsContent value="contact" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Contact Form */}
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center gap-3">
                  <MessageCircle className="h-5 w-5 text-blue-600" />
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900">Send us a Message</h2>
                    <p className="text-gray-600 text-sm mt-1">
                      Describe your issue and we'll get back to you as soon as possible
                    </p>
                  </div>
                </div>
              </div>
              <div className="p-6">
                <form onSubmit={handleContactSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">
                      Subject
                    </label>
                    <Input
                      placeholder="Brief description of your issue"
                      value={contactForm.subject}
                      onChange={(e) => setContactForm({...contactForm, subject: e.target.value})}
                      className="bg-gray-50 border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">
                      Message
                    </label>
                    <Textarea
                      placeholder="Please provide detailed information about your issue..."
                      rows={6}
                      value={contactForm.message}
                      onChange={(e) => setContactForm({...contactForm, message: e.target.value})}
                      className="bg-gray-50 border-gray-200 focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">
                      Priority
                    </label>
                    <select
                      className="w-full p-3 bg-gray-50 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      value={contactForm.priority}
                      onChange={(e) => setContactForm({...contactForm, priority: e.target.value})}
                    >
                      <option value="low">Low Priority</option>
                      <option value="medium">Medium Priority</option>
                      <option value="high">High Priority</option>
                      <option value="urgent">Urgent</option>
                    </select>
                  </div>
                  <Button
                    type="submit"
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Send Message
                  </Button>
                </form>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-white rounded-lg border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-green-600" />
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900">Other Ways to Reach Us</h2>
                    <p className="text-gray-600 text-sm mt-1">
                      Choose the method that works best for you
                    </p>
                  </div>
                </div>
              </div>
              <div className="p-6 space-y-4">
                {/* Email Support */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Mail className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-blue-900 mb-1">Email Support</h3>
                      <p className="text-blue-700 font-medium mb-2"><EMAIL></p>
                      <div className="flex items-center gap-2 text-sm text-blue-600">
                        <Clock className="h-4 w-4" />
                        <span>Response within 24 hours</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Phone Support */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Phone className="h-5 w-5 text-green-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-green-900 mb-1">Phone Support</h3>
                      <p className="text-green-700 font-medium mb-2">1-800-VESTRAL</p>
                      <div className="flex items-center gap-2 text-sm text-green-600">
                        <Clock className="h-4 w-4" />
                        <span>Mon-Fri 9AM-6PM EST</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Live Chat */}
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <MessageCircle className="h-5 w-5 text-purple-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-purple-900 mb-1">Live Chat</h3>
                      <p className="text-purple-700 font-medium mb-2">Available in-app</p>
                      <div className="flex items-center gap-2 text-sm text-purple-600">
                        <CheckCircle className="h-4 w-4" />
                        <span>Instant responses</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Resources Tab */}
        <TabsContent value="resources" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Documentation */}
            <div className="bg-white rounded-lg border border-gray-200 p-6 hover:bg-gray-50">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Documentation
                  </h3>
                  <p className="text-gray-600 text-sm mt-1">
                    Comprehensive guides and API documentation
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                className="w-full bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
              >
                View Documentation
                <ExternalLink className="h-4 w-4 ml-2" />
              </Button>
            </div>

            {/* Video Tutorials */}
            <div className="bg-white rounded-lg border border-gray-200 p-6 hover:bg-gray-50">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <Video className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Video Tutorials
                  </h3>
                  <p className="text-gray-600 text-sm mt-1">
                    Step-by-step video guides for all features
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                className="w-full bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100"
              >
                Watch Tutorials
                <ExternalLink className="h-4 w-4 ml-2" />
              </Button>
            </div>

            {/* System Status */}
            <div className="bg-white rounded-lg border border-gray-200 p-6 hover:bg-gray-50">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-green-100 rounded-lg">
                  <Shield className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    System Status
                  </h3>
                  <p className="text-gray-600 text-sm mt-1">
                    Check current system status and uptime
                  </p>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-700 font-medium">All Systems Operational</span>
                </div>
                <Button
                  variant="outline"
                  className="w-full bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
                >
                  View Status Page
                  <ExternalLink className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
