"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/components/ui/use-toast"
import { Dialog, DialogContent, DialogDescription, DialogTitle } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import SquarePaymentForm from "@/components/payment/SquarePaymentForm"
import {
  Shield,
  CheckCircle2,
  XCircle,
  Clock,
  AlertTriangle,
  Calendar,
  RefreshCw,
  Plus
} from "lucide-react"
import { apiClient } from "@/lib/api-client"

// Define the enum for verification request steps
type VerificationRequestSteps =
  | 'INITIATED'
  | 'SENT'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'FAILED'
  | 'CANCELLED'
  | 'REJECTED';

// Define the interface for verification requests
interface VerificationRequest {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  verificationNames: string[];
  step: VerificationRequestSteps;
  createdAt: string; // yyyy-MM-dd HH:mm:ss
}

// Define the interface for verification products
interface VerificationProduct {
  id: string;
  name: string;
  description: string;
  price: number;
}

export default function TenantVerificationsPage() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [requests, setRequests] = useState<VerificationRequest[]>([]);

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalStep, setModalStep] = useState<'products' | 'form' | 'payment'>('products');
  const [products, setProducts] = useState<VerificationProduct[]>([]);
  const [productsLoading, setProductsLoading] = useState(false);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);

  // Form state
  const [tenantForm, setTenantForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Payment state
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);

  // Fetch verification requests from API
  const fetchVerificationRequests = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await apiClient.get<VerificationRequest[]>('/verification-requests');
      setRequests(data);

    } catch (error) {
      console.error('Error fetching verification requests:', error);
      setError("Failed to fetch verification requests. Please try again later.");
      toast({
        title: "Error",
        description: "Failed to load verification requests.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch verification products for modal
  const fetchVerificationProducts = async () => {
    try {
      setProductsLoading(true);

      const data = await apiClient.get<VerificationProduct[]>('/tenants/products/en');
      setProducts(data);

    } catch (error) {
      console.error('Error fetching verification products:', error);
      toast({
        title: "Error",
        description: "Failed to load verification products.",
        variant: "destructive",
      });
    } finally {
      setProductsLoading(false);
    }
  };

  // Handle opening the modal
  const handleOpenModal = () => {
    setIsModalOpen(true);
    setModalStep('products');
    setSelectedProducts([]);
    setTenantForm({
      firstName: '',
      lastName: '',
      email: '',
      phone: ''
    });
    setFormErrors({});
    setPaymentLoading(false);
    setPaymentError(null);
    fetchVerificationProducts();
  };

  // Handle product selection
  const handleProductToggle = (productId: string) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  // Calculate total price of selected products
  const calculateTotalPrice = () => {
    return selectedProducts.reduce((total, productId) => {
      const product = products.find(p => p.id === productId);
      return total + (product ? product.price : 0);
    }, 0);
  };

  // Handle form input changes
  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setTenantForm(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!tenantForm.firstName.trim()) {
      errors.firstName = 'First name is required';
    }

    if (!tenantForm.lastName.trim()) {
      errors.lastName = 'Last name is required';
    }

    if (!tenantForm.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(tenantForm.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!tenantForm.phone.trim()) {
      errors.phone = 'Phone number is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle next button click (products -> form)
  const handleNext = () => {
    if (selectedProducts.length === 0) {
      toast({
        title: "No Products Selected",
        description: "Please select at least one verification product.",
        variant: "destructive",
      });
      return;
    }

    setModalStep('form');
  };

  // Handle form to payment step
  const handleFormToPayment = () => {
    if (!validateForm()) {
      return;
    }

    setModalStep('payment');
  };

  // Handle back button click
  const handleBack = () => {
    if (modalStep === 'form') {
      setModalStep('products');
    } else if (modalStep === 'payment') {
      setModalStep('form');
    }
  };

  // Handle successful payment
  const handlePaymentSuccess = async (paymentResult: any) => {
    try {
      setPaymentLoading(true);
      setPaymentError(null);

      console.log('Payment successful:', paymentResult);

      // TODO: Create verification request with the payment information
      // This would typically involve calling your backend API to:
      // 1. Store the payment information
      // 2. Create the verification request
      // 3. Send notifications to the tenant

      // For now, we'll simulate the request creation
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: "Payment Successful",
        description: `Verification request for ${tenantForm.firstName} ${tenantForm.lastName} has been created and paid.`,
      });

      // Close modal and reset
      setIsModalOpen(false);
      setModalStep('products');
      setSelectedProducts([]);
      setTenantForm({
        firstName: '',
        lastName: '',
        email: '',
        phone: ''
      });
      setFormErrors({});
      setPaymentLoading(false);
      setPaymentError(null);

      // Refresh the verification requests list
      fetchVerificationRequests();

    } catch (error) {
      console.error('Error creating verification request:', error);
      setPaymentError('Payment was successful, but there was an error creating the verification request. Please contact support.');
      toast({
        title: "Request Creation Failed",
        description: "Payment was processed but verification request creation failed. Please contact support.",
        variant: "destructive",
      });
    } finally {
      setPaymentLoading(false);
    }
  };

  // Handle payment errors
  const handlePaymentError = (error: string) => {
    setPaymentError(error);
    setPaymentLoading(false);
    toast({
      title: "Payment Failed",
      description: error,
      variant: "destructive",
    });
  };

  // Handle creating verification request (legacy - now handled in payment)
  const handleCreateRequest = () => {
    // This is now handled by the payment flow
    handleFormToPayment();
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchVerificationRequests();
  }, []);

  // Get status badge variant and text
  const getStatusInfo = (request: VerificationRequest) => {
    switch (request.step) {
      case 'COMPLETED':
        return {
          variant: "default" as const,
          text: "Completed",
          icon: CheckCircle2,
          className: "bg-green-100 text-green-800 border-green-200"
        };
      case 'IN_PROGRESS':
        return {
          variant: "secondary" as const,
          text: "In Progress",
          icon: Clock,
          className: "bg-blue-100 text-blue-800 border-blue-200"
        };
      case 'FAILED':
        return {
          variant: "destructive" as const,
          text: "Failed",
          icon: XCircle,
          className: "bg-red-100 text-red-800 border-red-200"
        };
      case 'REJECTED':
        return {
          variant: "destructive" as const,
          text: "Rejected",
          icon: XCircle,
          className: "bg-red-100 text-red-800 border-red-200"
        };
      case 'CANCELLED':
        return {
          variant: "outline" as const,
          text: "Cancelled",
          icon: XCircle,
          className: "bg-gray-100 text-gray-800 border-gray-200"
        };
      case 'SENT':
        return {
          variant: "secondary" as const,
          text: "Sent",
          icon: Clock,
          className: "bg-yellow-100 text-yellow-800 border-yellow-200"
        };
      case 'INITIATED':
        return {
          variant: "outline" as const,
          text: "Initiated",
          icon: AlertTriangle,
          className: "bg-gray-100 text-gray-800 border-gray-200"
        };
      default:
        return {
          variant: "outline" as const,
          text: request.step || "Unknown",
          icon: AlertTriangle,
          className: "bg-gray-100 text-gray-800 border-gray-200"
        };
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      // Parse the date string (yyyy-MM-dd HH:mm:ss)
      const date = new Date(dateString.replace(' ', 'T'));
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return dateString;
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center mb-4">
          <Shield className="h-6 w-6 text-gray-400 animate-pulse" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Tenant Verifications</h3>
        <p className="text-gray-600">Fetching verification products...</p>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
          <AlertTriangle className="h-6 w-6 text-red-600" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-3">Error Loading Verifications</h3>
        <p className="text-gray-600 mb-6 max-w-md text-center">{error}</p>
        <Button
          onClick={fetchVerificationRequests}
          className="bg-purple-600 hover:bg-purple-700 text-white"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 rounded-lg bg-purple-600 flex items-center justify-center">
              <Shield className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Tenant Verifications</h1>
              <p className="text-gray-600 mt-1">
                Manage and track tenant verification requests
              </p>
            </div>
          </div>
          <Button
            className="bg-purple-600 hover:bg-purple-700 text-white"
            onClick={handleOpenModal}
          >
            <Plus className="h-4 w-4 mr-2" />
            New Verification Request
          </Button>
        </div>
      </div>

      {/* Verification Requests List */}
      {requests.length === 0 ? (
        <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Verification Requests</h3>
          <p className="text-gray-600">No verification requests have been submitted yet.</p>
        </div>
      ) : (
        <div className="grid gap-6">
          {requests.map((request) => {
            const statusInfo = getStatusInfo(request);
            const StatusIcon = statusInfo.icon;

            return (
              <div key={request.id} className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {request.firstName} {request.lastName}
                      </h3>
                    </div>
                    <div className="space-y-2">
                      <p className="text-gray-600">
                        <span className="font-medium">Email:</span> {request.email}
                      </p>
                      <p className="text-gray-600">
                        <span className="font-medium">Phone:</span> {request.phone}
                      </p>
                      <div className="flex flex-wrap gap-2 mt-3">
                        {request.verificationNames.map((verification, index) => (
                          <Badge key={index} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                            {verification}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Badge className={statusInfo.className}>
                      <StatusIcon className="h-3 w-3 mr-1" />
                      {statusInfo.text}
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">Created At</p>
                      <p className="font-medium text-gray-900">{formatDate(request.createdAt)}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">Request ID</p>
                      <p className="font-medium text-gray-900 font-mono text-sm">{request.id}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <CheckCircle2 className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500">Verifications Count</p>
                      <p className="font-medium text-gray-900">{request.verificationNames.length}</p>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* New Verification Request Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] p-0 bg-white border border-gray-200 shadow-lg overflow-hidden flex flex-col">
          {/* Modal Header - Fixed */}
          <div className="flex-shrink-0 p-6 border-b border-gray-200">
            <div className="flex items-center gap-4">
              <div className="h-10 w-10 rounded-lg bg-purple-600 flex items-center justify-center">
                <Plus className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold text-gray-900">
                  {modalStep === 'products' ? 'New Verification Request' :
                   modalStep === 'form' ? 'Tenant Information' : 'Payment'}
                </DialogTitle>
                <DialogDescription className="text-gray-600 mt-1">
                  {modalStep === 'products'
                    ? 'Select the verification products you want to request for a tenant.'
                    : modalStep === 'form'
                    ? 'Enter the tenant\'s contact information for the verification request.'
                    : 'Complete payment to create the verification request.'
                  }
                </DialogDescription>
              </div>
            </div>
          </div>

          {/* Modal Content - Scrollable */}
          <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            <div className="p-6">
            {modalStep === 'products' ? (
              // Products Selection Step
              <>
                {productsLoading ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <div className="h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                      <Shield className="h-6 w-6 text-gray-400 animate-pulse" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Products</h3>
                    <p className="text-gray-600">Fetching verification products...</p>
                  </div>
                ) : products.length === 0 ? (
                  <div className="text-center py-12">
                    <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Products Available</h3>
                    <p className="text-gray-600">No verification products are currently available.</p>
                  </div>
                ) : (
                  <div className="space-y-4 max-h-80 overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                    {products.map((product) => (
                      <div key={product.id} className="bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div className="flex items-start gap-3">
                          <Checkbox
                            id={product.id}
                            checked={selectedProducts.includes(product.id)}
                            onCheckedChange={() => handleProductToggle(product.id)}
                            className="mt-1 data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600"
                          />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-2">
                              <label
                                htmlFor={product.id}
                                className="text-base font-semibold text-gray-900 cursor-pointer"
                              >
                                {product.name}
                              </label>
                              <div className="flex items-center gap-1">
                                <span className="text-sm text-gray-500">$</span>
                                <span className="text-base font-semibold text-gray-900">
                                  {product.price.toFixed(2)}
                                </span>
                              </div>
                            </div>
                            <p className="text-sm text-gray-600 leading-relaxed">{product.description}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </>
            ) : modalStep === 'form' ? (
              // Tenant Information Form Step
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName" className="text-gray-900 font-medium">First Name</Label>
                    <Input
                      id="firstName"
                      name="firstName"
                      value={tenantForm.firstName}
                      onChange={handleFormChange}
                      className="bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="Enter first name"
                    />
                    {formErrors.firstName && (
                      <p className="text-sm text-red-600">{formErrors.firstName}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lastName" className="text-gray-900 font-medium">Last Name</Label>
                    <Input
                      id="lastName"
                      name="lastName"
                      value={tenantForm.lastName}
                      onChange={handleFormChange}
                      className="bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      placeholder="Enter last name"
                    />
                    {formErrors.lastName && (
                      <p className="text-sm text-red-600">{formErrors.lastName}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="text-gray-900 font-medium">Email Address</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={tenantForm.email}
                    onChange={handleFormChange}
                    className="bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="Enter email address"
                  />
                  {formErrors.email && (
                    <p className="text-sm text-red-600">{formErrors.email}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-gray-900 font-medium">Phone Number</Label>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    value={tenantForm.phone}
                    onChange={handleFormChange}
                    className="bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    placeholder="Enter phone number"
                  />
                  {formErrors.phone && (
                    <p className="text-sm text-red-600">{formErrors.phone}</p>
                  )}
                </div>

                {/* Selected Products Summary */}
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">Selected Verifications:</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedProducts.map((productId) => {
                      const product = products.find(p => p.id === productId);
                      return product ? (
                        <span key={productId} className="inline-flex items-center px-2 py-1 rounded-md bg-purple-100 text-purple-800 text-xs font-medium">
                          {product.name}
                        </span>
                      ) : null;
                    })}
                  </div>
                </div>
              </div>
            ) : (
              // Payment Step
              <div className="space-y-6">
                {/* Order Summary */}
                <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h4>

                  {/* Tenant Information */}
                  <div className="mb-4 pb-4 border-b border-gray-200">
                    <h5 className="text-sm font-medium text-gray-700 mb-2">Tenant Information</h5>
                    <p className="text-sm text-gray-600">
                      {tenantForm.firstName} {tenantForm.lastName}
                    </p>
                    <p className="text-sm text-gray-600">{tenantForm.email}</p>
                    <p className="text-sm text-gray-600">{tenantForm.phone}</p>
                  </div>

                  {/* Selected Verifications */}
                  <div className="mb-4 pb-4 border-b border-gray-200">
                    <h5 className="text-sm font-medium text-gray-700 mb-2">Selected Verifications</h5>
                    <div className="space-y-2">
                      {selectedProducts.map((productId) => {
                        const product = products.find(p => p.id === productId);
                        return product ? (
                          <div key={productId} className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">{product.name}</span>
                            <span className="text-sm font-medium text-gray-900">${product.price.toFixed(2)}</span>
                          </div>
                        ) : null;
                      })}
                    </div>
                  </div>

                  {/* Total */}
                  <div className="flex justify-between items-center">
                    <span className="text-base font-semibold text-gray-900">Total</span>
                    <span className="text-lg font-bold text-gray-900">${calculateTotalPrice().toFixed(2)}</span>
                  </div>
                </div>

                {/* Payment Error */}
                {paymentError && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <p className="text-sm text-red-600">{paymentError}</p>
                  </div>
                )}

                {/* Square Payment Form */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Payment Information</h4>

                  <SquarePaymentForm
                    amount={calculateTotalPrice()}
                    onPaymentSuccess={handlePaymentSuccess}
                    onPaymentError={handlePaymentError}
                    loading={paymentLoading}
                    disabled={paymentLoading}
                  />
                </div>
              </div>
            )}
            </div>
          </div>

          {/* Modal Footer - Fixed */}
          <div className="flex-shrink-0 p-6 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                {modalStep === 'products' ? (
                  selectedProducts.length > 0 ? (
                    <div className="space-y-1">
                      <span className="font-medium block">
                        {selectedProducts.length} product{selectedProducts.length > 1 ? 's' : ''} selected
                      </span>
                      <div className="flex items-center gap-1">
                        <span className="text-gray-500">Total:</span>
                        <span className="font-semibold text-gray-900">
                          ${calculateTotalPrice().toFixed(2)}
                        </span>
                      </div>
                    </div>
                  ) : (
                    <span>Select products to create a verification request</span>
                  )
                ) : modalStep === 'form' ? (
                  <div className="space-y-1">
                    <span className="font-medium block">
                      {selectedProducts.length} verification{selectedProducts.length > 1 ? 's' : ''} selected
                    </span>
                    <div className="flex items-center gap-1">
                      <span className="text-gray-500">Total:</span>
                      <span className="font-semibold text-gray-900">
                        ${calculateTotalPrice().toFixed(2)}
                      </span>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-1">
                    <span className="font-medium block">Ready to pay</span>
                    <div className="flex items-center gap-1">
                      <span className="text-gray-500">Amount:</span>
                      <span className="font-semibold text-gray-900">
                        ${calculateTotalPrice().toFixed(2)}
                      </span>
                    </div>
                  </div>
                )}
              </div>
              <div className="flex gap-3">
                {(modalStep === 'form' || modalStep === 'payment') && (
                  <Button
                    variant="outline"
                    onClick={handleBack}
                    disabled={paymentLoading}
                    className="border-gray-300 text-gray-700 hover:bg-gray-50"
                  >
                    Back
                  </Button>
                )}
                {modalStep !== 'payment' && (
                  <Button
                    variant="outline"
                    onClick={() => setIsModalOpen(false)}
                    className="border-red-300 text-red-600 hover:bg-red-50"
                  >
                    Cancel
                  </Button>
                )}
                {modalStep !== 'payment' && (
                  <Button
                    onClick={modalStep === 'products' ? handleNext : handleCreateRequest}
                    disabled={modalStep === 'products' ? selectedProducts.length === 0 : false}
                    className="bg-purple-600 hover:bg-purple-700 text-white disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
                  >
                    {modalStep === 'products' ? 'Next' : 'Continue to Payment'}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
