"use client"

import { useState, useEffect } from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import {
  Download,
  RefreshCw,
  Search,
  Table,
  SortAsc,
  SortDesc,
  Info,
  MapPin,
  ChevronLeft,
  ChevronRight
} from "lucide-react"
import { api } from "@/lib/api-instance"

// Types for API responses
interface CityStatistic {
  id: number
  name: string
  numberOfBedrooms: number
  averagePrice: number
}

interface PostalCodeStatistic {
  id: number
  name: string // This will be the postal code
  numberOfBedrooms: number
  averagePrice: number
}

interface PaginatedResponse<T> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
}

export default function QuebecDataPage() {
  const [loading, setLoading] = useState(true)
  const [citiesData, setCitiesData] = useState<CityStatistic[]>([])
  const [postalCodesData, setPostalCodesData] = useState<PostalCodeStatistic[]>([])
  const [citiesFilter, setCitiesFilter] = useState("")
  const [postalCodesFilter, setPostalCodesFilter] = useState("")
  const [citiesSortField, setCitiesSortField] = useState<keyof CityStatistic>("name")
  const [citiesSortDirection, setCitiesSortDirection] = useState<"asc" | "desc">("asc")
  const [postalCodesSortField, setPostalCodesSortField] = useState<keyof PostalCodeStatistic>("name")
  const [postalCodesSortDirection, setPostalCodesSortDirection] = useState<"asc" | "desc">("asc")
  const [citiesPageSize, setCitiesPageSize] = useState(50)
  const [postalCodesPageSize, setPostalCodesPageSize] = useState(50)
  const [citiesCurrentPage, setCitiesCurrentPage] = useState(0)
  const [postalCodesCurrentPage, setPostalCodesCurrentPage] = useState(0)

  // Filter and sort cities data
  const allFilteredCities = citiesData
    .filter(city =>
      city.name.toLowerCase().includes(citiesFilter.toLowerCase())
    )
    .sort((a, b) => {
      const aValue = a[citiesSortField]
      const bValue = b[citiesSortField]

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return citiesSortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return citiesSortDirection === 'asc'
          ? aValue - bValue
          : bValue - aValue
      }

      return 0
    })

  // Paginate cities data
  const citiesStartIndex = citiesCurrentPage * citiesPageSize
  const citiesEndIndex = citiesStartIndex + citiesPageSize
  const filteredCities = allFilteredCities.slice(citiesStartIndex, citiesEndIndex)
  const citiesTotalPages = Math.ceil(allFilteredCities.length / citiesPageSize)

  // Filter and sort postal codes data
  const allFilteredPostalCodes = postalCodesData
    .filter(pc =>
      pc.name.toLowerCase().includes(postalCodesFilter.toLowerCase())
    )
    .sort((a, b) => {
      const aValue = a[postalCodesSortField]
      const bValue = b[postalCodesSortField]

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return postalCodesSortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue)
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return postalCodesSortDirection === 'asc'
          ? aValue - bValue
          : bValue - aValue
      }

      return 0
    })

  // Paginate postal codes data
  const postalCodesStartIndex = postalCodesCurrentPage * postalCodesPageSize
  const postalCodesEndIndex = postalCodesStartIndex + postalCodesPageSize
  const filteredPostalCodes = allFilteredPostalCodes.slice(postalCodesStartIndex, postalCodesEndIndex)
  const postalCodesTotalPages = Math.ceil(allFilteredPostalCodes.length / postalCodesPageSize)

  // Handle sorting for cities
  const handleCitiesSort = (field: keyof CityStatistic) => {
    if (citiesSortField === field) {
      setCitiesSortDirection(citiesSortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setCitiesSortField(field)
      setCitiesSortDirection('asc')
    }
  }

  // Handle sorting for postal codes
  const handlePostalCodesSort = (field: keyof PostalCodeStatistic) => {
    if (postalCodesSortField === field) {
      setPostalCodesSortDirection(postalCodesSortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setPostalCodesSortField(field)
      setPostalCodesSortDirection('asc')
    }
  }

  // Fetch cities statistics with pagination
  const fetchCitiesStatistics = async (page = 0, size = 100) => {
    try {
      const response = await api.get<PaginatedResponse<CityStatistic>>(`/statistics/cities?page=${page}&size=${size}`)
      if (response.data) {
        return response.data
      }
      return null
    } catch (error) {
      console.error('Error fetching cities statistics:', error)
      return null
    }
  }

  // Fetch postal codes statistics with pagination
  const fetchPostalCodesStatistics = async (page = 0, size = 100) => {
    try {
      const response = await api.get<PaginatedResponse<PostalCodeStatistic>>(`/statistics/postal-codes?page=${page}&size=${size}`)
      if (response.data) {
        return response.data
      }
      return null
    } catch (error) {
      console.error('Error fetching postal codes statistics:', error)
      return null
    }
  }

  // Fetch all pages of data
  const fetchAllData = async () => {
    setLoading(true)
    try {
      // Fetch cities data
      let allCities: CityStatistic[] = []
      let citiesPage = 0
      let hasMoreCities = true

      while (hasMoreCities) {
        const citiesResponse = await fetchCitiesStatistics(citiesPage, 100)
        if (citiesResponse && citiesResponse.content.length > 0) {
          allCities = [...allCities, ...citiesResponse.content]
          citiesPage++
          hasMoreCities = citiesPage < citiesResponse.totalPages
        } else {
          hasMoreCities = false
        }
      }

      // Fetch postal codes data
      let allPostalCodes: PostalCodeStatistic[] = []
      let postalCodesPage = 0
      let hasMorePostalCodes = true

      while (hasMorePostalCodes) {
        const postalCodesResponse = await fetchPostalCodesStatistics(postalCodesPage, 100)
        if (postalCodesResponse && postalCodesResponse.content.length > 0) {
          allPostalCodes = [...allPostalCodes, ...postalCodesResponse.content]
          postalCodesPage++
          hasMorePostalCodes = postalCodesPage < postalCodesResponse.totalPages
        } else {
          hasMorePostalCodes = false
        }
      }

      setCitiesData(allCities)
      setPostalCodesData(allPostalCodes)
      console.log('Fetched cities:', allCities.length)
      console.log('Fetched postal codes:', allPostalCodes.length)
    } catch (error) {
      console.error('Error fetching statistics data:', error)
    } finally {
      setLoading(false)
    }
  }

  // Load data on component mount
  useEffect(() => {
    fetchAllData()
  }, [])

  // Convert bedrooms to size format (1 bedroom = 3 1/2, 2 bedrooms = 4 1/2, etc.)
  const convertBedroomsToSize = (bedrooms: number) => {
    const size = bedrooms + 2.5
    const wholeNumber = Math.floor(size)
    const fraction = size - wholeNumber

    if (fraction === 0.5) {
      return `${wholeNumber} 1/2`
    } else if (fraction === 0) {
      return wholeNumber.toString()
    } else {
      // Handle other fractions if needed
      return size.toString()
    }
  }



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="h-10 w-10 rounded-lg bg-purple-600 flex items-center justify-center">
              <Table className="h-5 w-5 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Quebec Rental Market</h1>
              <p className="text-gray-600 mt-1">
                Comprehensive rental market data across Quebec cities and postal codes
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm" className="border-gray-200 hover:bg-gray-50">
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchAllData}
              disabled={loading}
              className="border-gray-200 hover:bg-gray-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-4 mt-4">
          <Badge variant="secondary" className="bg-gray-100 text-gray-700 border-gray-200">
            {citiesData.length} Cities
          </Badge>
          <Badge variant="secondary" className="bg-gray-100 text-gray-700 border-gray-200">
            {postalCodesData.length} Postal Codes
          </Badge>
          {!loading && (
            <Badge variant="secondary" className="bg-green-100 text-green-700 border-green-200">
              Live Data
            </Badge>
          )}
        </div>
      </div>

      {/* Informative Message */}
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-amber-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-amber-900 mb-1">Important Information About Rental Prices</h4>
            <p className="text-amber-800 text-sm">
              The prices displayed on this page are based on rental listings collected over the past few months in Quebec.
              Actual rental prices may vary depending on several factors such as whether the property is furnished or not,
              the construction year, included services (e.g. heating, electricity), location specifics, and market demand.
            </p>
          </div>
        </div>
      </div>

      {/* Data Tables */}
      <div className="grid gap-6">
        {/* Cities Table */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Cities Statistics</h2>
              <p className="text-gray-500 text-sm mt-1">Rental market data across Quebec cities</p>
            </div>
            <Badge variant="secondary" className="bg-gray-100 text-gray-700 border-gray-200">
              {allFilteredCities.length} Results
            </Badge>
          </div>
          {/* Cities Filter and Controls */}
          <div className="mb-4 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="relative max-w-sm flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search cities..."
                value={citiesFilter}
                onChange={(e) => {
                  setCitiesFilter(e.target.value)
                  setCitiesCurrentPage(0)
                }}
                className="pl-10 bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Show:</span>
                <Select
                  value={citiesPageSize.toString()}
                  onValueChange={(value) => {
                    setCitiesPageSize(parseInt(value))
                    setCitiesCurrentPage(0)
                  }}
                >
                  <SelectTrigger className="w-20 bg-gray-50 border-gray-200">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                    <SelectItem value="200">200</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Cities Table */}
          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50 border-b border-gray-200">
                    <th
                      className="text-left p-4 font-medium text-gray-900 cursor-pointer hover:bg-gray-100"
                      onClick={() => handleCitiesSort('name')}
                    >
                      <div className="flex items-center gap-2">
                        <span>City Name</span>
                        {citiesSortField === 'name' && (
                          citiesSortDirection === 'asc' ?
                          <SortAsc className="h-4 w-4 text-gray-600" /> :
                          <SortDesc className="h-4 w-4 text-gray-600" />
                        )}
                      </div>
                    </th>
                    <th
                      className="text-left p-4 font-medium text-gray-900 cursor-pointer hover:bg-gray-100"
                      onClick={() => handleCitiesSort('averagePrice')}
                    >
                      <div className="flex items-center gap-2">
                        <span>Average Price</span>
                        {citiesSortField === 'averagePrice' && (
                          citiesSortDirection === 'asc' ?
                          <SortAsc className="h-4 w-4 text-gray-600" /> :
                          <SortDesc className="h-4 w-4 text-gray-600" />
                        )}
                      </div>
                    </th>
                    <th
                      className="text-left p-4 font-medium text-gray-900 cursor-pointer hover:bg-gray-100"
                      onClick={() => handleCitiesSort('numberOfBedrooms')}
                    >
                      <div className="flex items-center gap-2">
                        <span>Size</span>
                        {citiesSortField === 'numberOfBedrooms' && (
                          citiesSortDirection === 'asc' ?
                          <SortAsc className="h-4 w-4 text-gray-600" /> :
                          <SortDesc className="h-4 w-4 text-gray-600" />
                        )}
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={3} className="text-center py-12">
                        <div className="flex flex-col items-center gap-3">
                          <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
                          <p className="text-gray-600">Loading cities data...</p>
                        </div>
                      </td>
                    </tr>
                  ) : filteredCities.length === 0 ? (
                    <tr>
                      <td colSpan={3} className="text-center py-12">
                        <div className="flex flex-col items-center gap-3">
                          <Search className="h-6 w-6 text-gray-400" />
                          <p className="text-gray-600">No cities found matching your search.</p>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    filteredCities.map((city) => (
                      <tr key={city.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="p-4">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-purple-600 rounded-lg">
                              <MapPin className="h-4 w-4 text-white" />
                            </div>
                            <span className="font-medium text-gray-900">{city.name}</span>
                          </div>
                        </td>
                        <td className="p-4">
                          <span className="text-gray-900 font-semibold">${city.averagePrice.toLocaleString()}</span>
                          <span className="text-gray-500 text-sm ml-1">/month</span>
                        </td>
                        <td className="p-4">
                          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                            {convertBedroomsToSize(city.numberOfBedrooms)}
                          </Badge>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Cities Pagination */}
          {!loading && allFilteredCities.length > 0 && (
            <div className="mt-4 flex flex-col sm:flex-row items-center justify-between gap-4 border-t border-gray-200 pt-4">
              <div className="text-sm text-gray-600">
                Showing {citiesStartIndex + 1} to {Math.min(citiesEndIndex, allFilteredCities.length)} of {allFilteredCities.length} cities
              </div>

              {citiesTotalPages > 1 && (
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCitiesCurrentPage(Math.max(0, citiesCurrentPage - 1))}
                    disabled={citiesCurrentPage === 0}
                    className="border-gray-200 hover:bg-gray-50"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, citiesTotalPages) }, (_, i) => {
                      let pageNum
                      if (citiesTotalPages <= 5) {
                        pageNum = i
                      } else if (citiesCurrentPage < 3) {
                        pageNum = i
                      } else if (citiesCurrentPage >= citiesTotalPages - 3) {
                        pageNum = citiesTotalPages - 5 + i
                      } else {
                        pageNum = citiesCurrentPage - 2 + i
                      }

                      return (
                        <Button
                          key={pageNum}
                          variant={citiesCurrentPage === pageNum ? "default" : "outline"}
                          size="sm"
                          onClick={() => setCitiesCurrentPage(pageNum)}
                          className={`w-8 h-8 p-0 ${
                            citiesCurrentPage === pageNum
                              ? "bg-purple-600 hover:bg-purple-700 text-white"
                              : "border-gray-200 hover:bg-gray-50"
                          }`}
                        >
                          {pageNum + 1}
                        </Button>
                      )
                    })}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCitiesCurrentPage(Math.min(citiesTotalPages - 1, citiesCurrentPage + 1))}
                    disabled={citiesCurrentPage === citiesTotalPages - 1}
                    className="border-gray-200 hover:bg-gray-50"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Postal Codes Table */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Postal Codes Statistics</h2>
              <p className="text-gray-500 text-sm mt-1">Detailed rental data by postal code</p>
            </div>
            <Badge variant="secondary" className="bg-gray-100 text-gray-700 border-gray-200">
              {allFilteredPostalCodes.length} Results
            </Badge>
          </div>
          {/* Postal Codes Filter and Controls */}
          <div className="mb-4 flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="relative max-w-sm flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search postal codes..."
                value={postalCodesFilter}
                onChange={(e) => {
                  setPostalCodesFilter(e.target.value)
                  setPostalCodesCurrentPage(0)
                }}
                className="pl-10 bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Show:</span>
                <Select
                  value={postalCodesPageSize.toString()}
                  onValueChange={(value) => {
                    setPostalCodesPageSize(parseInt(value))
                    setPostalCodesCurrentPage(0)
                  }}
                >
                  <SelectTrigger className="w-20 bg-gray-50 border-gray-200">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                    <SelectItem value="200">200</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Postal Codes Table */}
          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-50 border-b border-gray-200">
                    <th
                      className="text-left p-4 font-medium text-gray-900 cursor-pointer hover:bg-gray-100"
                      onClick={() => handlePostalCodesSort('name')}
                    >
                      <div className="flex items-center gap-2">
                        <span>Postal Code</span>
                        {postalCodesSortField === 'name' && (
                          postalCodesSortDirection === 'asc' ?
                          <SortAsc className="h-4 w-4 text-gray-600" /> :
                          <SortDesc className="h-4 w-4 text-gray-600" />
                        )}
                      </div>
                    </th>
                    <th
                      className="text-left p-4 font-medium text-gray-900 cursor-pointer hover:bg-gray-100"
                      onClick={() => handlePostalCodesSort('averagePrice')}
                    >
                      <div className="flex items-center gap-2">
                        <span>Average Price</span>
                        {postalCodesSortField === 'averagePrice' && (
                          postalCodesSortDirection === 'asc' ?
                          <SortAsc className="h-4 w-4 text-gray-600" /> :
                          <SortDesc className="h-4 w-4 text-gray-600" />
                        )}
                      </div>
                    </th>
                    <th
                      className="text-left p-4 font-medium text-gray-900 cursor-pointer hover:bg-gray-100"
                      onClick={() => handlePostalCodesSort('numberOfBedrooms')}
                    >
                      <div className="flex items-center gap-2">
                        <span>Size</span>
                        {postalCodesSortField === 'numberOfBedrooms' && (
                          postalCodesSortDirection === 'asc' ?
                          <SortAsc className="h-4 w-4 text-gray-600" /> :
                          <SortDesc className="h-4 w-4 text-gray-600" />
                        )}
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan={3} className="text-center py-12">
                        <div className="flex flex-col items-center gap-3">
                          <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
                          <p className="text-gray-600">Loading postal codes data...</p>
                        </div>
                      </td>
                    </tr>
                  ) : filteredPostalCodes.length === 0 ? (
                    <tr>
                      <td colSpan={3} className="text-center py-12">
                        <div className="flex flex-col items-center gap-3">
                          <Search className="h-6 w-6 text-gray-400" />
                          <p className="text-gray-600">No postal codes found matching your search.</p>
                        </div>
                      </td>
                    </tr>
                  ) : (
                    filteredPostalCodes.map((pc) => (
                      <tr key={pc.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="p-4">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-purple-600 rounded-lg">
                              <MapPin className="h-4 w-4 text-white" />
                            </div>
                            <span className="font-medium text-gray-900 font-mono">{pc.name}</span>
                          </div>
                        </td>
                        <td className="p-4">
                          <span className="text-gray-900 font-semibold">${pc.averagePrice.toLocaleString()}</span>
                          <span className="text-gray-500 text-sm ml-1">/month</span>
                        </td>
                        <td className="p-4">
                          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                            {convertBedroomsToSize(pc.numberOfBedrooms)}
                          </Badge>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* Postal Codes Pagination */}
          {!loading && allFilteredPostalCodes.length > 0 && (
            <div className="mt-4 flex flex-col sm:flex-row items-center justify-between gap-4 border-t border-gray-200 pt-4">
              <div className="text-sm text-gray-600">
                Showing {postalCodesStartIndex + 1} to {Math.min(postalCodesEndIndex, allFilteredPostalCodes.length)} of {allFilteredPostalCodes.length} postal codes
              </div>

              {postalCodesTotalPages > 1 && (
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPostalCodesCurrentPage(Math.max(0, postalCodesCurrentPage - 1))}
                    disabled={postalCodesCurrentPage === 0}
                    className="border-gray-200 hover:bg-gray-50"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>

                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(5, postalCodesTotalPages) }, (_, i) => {
                      let pageNum
                      if (postalCodesTotalPages <= 5) {
                        pageNum = i
                      } else if (postalCodesCurrentPage < 3) {
                        pageNum = i
                      } else if (postalCodesCurrentPage >= postalCodesTotalPages - 3) {
                        pageNum = postalCodesTotalPages - 5 + i
                      } else {
                        pageNum = postalCodesCurrentPage - 2 + i
                      }

                      return (
                        <Button
                          key={pageNum}
                          variant={postalCodesCurrentPage === pageNum ? "default" : "outline"}
                          size="sm"
                          onClick={() => setPostalCodesCurrentPage(pageNum)}
                          className={`w-8 h-8 p-0 ${
                            postalCodesCurrentPage === pageNum
                              ? "bg-purple-600 hover:bg-purple-700 text-white"
                              : "border-gray-200 hover:bg-gray-50"
                          }`}
                        >
                          {pageNum + 1}
                        </Button>
                      )
                    })}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPostalCodesCurrentPage(Math.min(postalCodesTotalPages - 1, postalCodesCurrentPage + 1))}
                    disabled={postalCodesCurrentPage === postalCodesTotalPages - 1}
                    className="border-gray-200 hover:bg-gray-50"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
