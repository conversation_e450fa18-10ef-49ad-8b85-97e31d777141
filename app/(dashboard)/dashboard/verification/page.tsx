"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { useRouter } from "next/navigation"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/components/ui/use-toast"
import { 
  AlertCircle, 
  CheckCircle2, 
  Clock, 
  FileText, 
  Upload, 
  ShieldCheck, 
  AlertTriangle, 

} from "lucide-react"
import Cookies from "js-cookie"
import env from "@/lib/env"
import { format } from "date-fns"
import Webcam from "react-webcam"

// Define dashboard data interface (same as in dashboard page)
interface DashboardData {
  email: string;
  businessType: 'COMPANY' | 'INDIVIDUAL';
  name: string;
  phone: string;
  preferredLanguage: string;
  emailConfirmed: boolean;
  idVerified: boolean;
  activePropertiesNumber: number;
  visitNumber: number;
  applicationNumber: number;
}

// Define business verification form data
interface BusinessVerificationData {
  businessName: string;
  neq: string;
  registrationNumber: string;
  legalStructure: string;
  description: string;
  documents: File[];
}

// Define individual verification form data
interface IndividualVerificationData {
  firstName: string;
  lastName: string;
  idType: string;
  idDocument: File | null;
  selfie: File | null;
}

// Define verification status data
interface VerificationStatusData {
  verificationDate: string | null;
  status: 'IN_PROGRESS' | 'NOT_STARTED' | 'COMPLETED' | 'FAILED';
}

export default function VerificationPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [verificationStatus, setVerificationStatus] = useState<VerificationStatusData | null>(null);
  const [showVerificationForm, setShowVerificationForm] = useState(false);
  
  // Form state
  const [businessData, setBusinessData] = useState<BusinessVerificationData>({
    businessName: '',
    neq: '',
    registrationNumber: '',
    legalStructure: 'corporation',
    description: '',
    documents: []
  });
  
  const [individualData, setIndividualData] = useState<IndividualVerificationData>({
    firstName: '',
    lastName: '',
    idType: 'PASSPORT',
    idDocument: null,
    selfie: null
  });
  
  const [selectedFiles] = useState<File[]>([]);
  const [fileNames] = useState<string[]>([]);
  
  // State for consent checkbox
  const [consentChecked, setConsentChecked] = useState(false);

  // State for selfie capture
  const [selfieCapture, setSelfieCapture] = useState<string | null>(null);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const webcamRef = useRef<Webcam>(null);

  // Function to capture selfie
  const captureSelfie = useCallback(() => {
    if (webcamRef.current) {
      const imageSrc = webcamRef.current.getScreenshot();
      if (imageSrc) {
        setSelfieCapture(imageSrc);
        
        // Convert data URL to File object
        fetch(imageSrc)
          .then(res => res.blob())
          .then(blob => {
            const file = new File([blob], "selfie.jpg", { type: "image/jpeg" });
            setIndividualData(prev => ({...prev, selfie: file}));
          })
          .catch(err => {
            console.error("Error creating file from selfie:", err);
            toast({
              title: "Error",
              description: "Failed to process selfie. Please try again.",
              variant: "destructive",
            });
          });
      }
      setIsCameraOpen(false);
    }
  }, [webcamRef, toast, setIndividualData]);

  // Function to retake selfie
  const retakeSelfie = useCallback(() => {
    setSelfieCapture(null);
    setIndividualData(prev => ({...prev, selfie: null}));
    setIsCameraOpen(true);
  }, [setIndividualData]);

  // Function to start camera
  const startCamera = async () => {
    try {
      setIsCameraOpen(true);
    } catch (err) {
      console.error("Error accessing camera:", err);
      toast({
        title: "Camera Error",
        description: "Unable to access your camera. Please check permissions and try again.",
        variant: "destructive",
      });
    }
  };

  // Clean up camera resources when component unmounts
  useEffect(() => {
    return () => {
      setIsCameraOpen(false);
    };
  }, []);

  // Fetch verification status from API
  const fetchVerificationStatus = async () => {
    try {
      // Get authentication token
      const idToken = Cookies.get('id_token');
      const session = Cookies.get('session');
      const token = idToken || session;
      
      if (!token) {
        setError("You are not authenticated. Please log in again.");
        return;
      }
      
      // Determine the correct endpoint based on business type
      const endpoint = dashboardData?.businessType === 'COMPANY' 
        ? `${env.API_URL}/accounts/verification/status` 
        : `${env.API_URL}/accounts/id-verification`;
      
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          setError("Your session has expired. Please log in again.");
          return;
        }
        throw new Error(`Error: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Update verification status
      setVerificationStatus({
        verificationDate: data.verificationDate,
        status: data.status
      });
      
      // If verification is completed, hide the form
      if (data.status === 'COMPLETED') {
        setShowVerificationForm(false);
      } else {
        setShowVerificationForm(true);
      }
      
    } catch (error) {
      console.error('Error fetching verification status:', error);
      setError("Failed to fetch verification status. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  // Fetch dashboard data from API
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Get authentication token
        const idToken = Cookies.get('id_token');
        const session = Cookies.get('session');
        const token = idToken || session;
        
        if (!token) {
          setError("You are not authenticated. Please log in again.");
          router.push('/login');
          return;
        }
        
        const response = await fetch(`${env.API_URL}/accounts/dashboard`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (response.status === 401 || response.status === 403) {
          // Token expired or invalid
          setError("Your session has expired. Please log in again.");
          router.push('/login');
          return;
        }
        
        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }
        
        const data = await response.json();
        setDashboardData(data);
        
        // Pre-fill business name if available
        if (data.businessType === 'COMPANY' && data.name) {
          setBusinessData(prev => ({
            ...prev,
            businessName: data.name
          }));
        }
        
        // Pre-fill individual name if available
        if (data.businessType === 'INDIVIDUAL' && data.name) {
          const nameParts = data.name.split(' ');
          setIndividualData(prev => ({
            ...prev,
            firstName: nameParts[0] || '',
            lastName: nameParts.slice(1).join(' ') || ''
          }));
        }
        
        // Fetch verification status after dashboard data
        await fetchVerificationStatus();
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError("Failed to load account data. Please try again.");
        toast({
          title: "Error",
          description: "Failed to load account data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchDashboardData();
  }, [router, toast]);

  // Handle business form input changes
  const handleBusinessInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setBusinessData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle individual form input changes
  const handleIndividualInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setIndividualData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Handle legal structure selection
  const handleLegalStructureChange = (value: string) => {
    setBusinessData(prev => ({
      ...prev,
      legalStructure: value
    }));
  };
  
  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, field: 'idDocument' | 'selfie' = 'idDocument') => {
    if (e.target.files) {
      const newFile = e.target.files[0];
      
      // Update state with the new file
      if (field === 'idDocument') {
        setIndividualData(prev => ({...prev, idDocument: newFile}));
      } else {
        setIndividualData(prev => ({...prev, selfie: newFile}));
      }
      
      // Reset the file input so the same file can be selected again if needed
      e.target.value = '';
    }
  };
  


  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (submitting) {
      return;
    }
    
    // Validate file selection
    if (!individualData.idDocument) {
      toast({
        title: "ID Document Required",
        description: "Please upload a valid ID document for verification.",
        variant: "destructive",
      });
      return;
    }
    
    // Validate selfie
    if (!selfieCapture) {
      toast({
        title: "Selfie Required",
        description: "Please take a selfie for verification.",
        variant: "destructive",
      });
      return;
    }
    
    // Validate consent
    if (!consentChecked) {
      toast({
        title: "Consent Required",
        description: "Please check the consent checkbox to proceed.",
        variant: "destructive",
      });
      return;
    }
    
    setSubmitting(true);
    
    try {
      // Get authentication token
      const idToken = Cookies.get('id_token');
      const session = Cookies.get('session');
      const token = idToken || session;
      
      if (!token) {
        toast({
          title: "Authentication Error",
          description: "You are not authenticated. Please log in again.",
          variant: "destructive",
        });
        setSubmitting(false);
        return;
      }
      
      // Create form data for multipart/form-data request
      const formData = new FormData();
      
      // Add verification request data as JSON
      const verificationRequest = {
        firstName: individualData.firstName,
        lastName: individualData.lastName,
        idType: individualData.idType
      };
      
      // Convert selfie capture from base64 to File object
      const selfieFile = await fetch(selfieCapture)
        .then(res => res.blob())
        .then(blob => new File([blob], "selfie.jpg", { type: "image/jpeg" }));
      
      // Add verification request as a part
      formData.append('verificationRequest', JSON.stringify(verificationRequest));
      
      // Add selfie
      formData.append('selfie', selfieFile);
      
      // Add ID document
      formData.append('idDocument', individualData.idDocument);
      
      // Send request to API
      const response = await fetch(`${env.API_URL}/accounts/verification/individual`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      // Show success message
      toast({
        title: "Verification Submitted",
        description: "Your verification request has been submitted successfully.",
      });
      
      // Refresh verification status
      await fetchVerificationStatus();
      
      // Reset form
      setIndividualData({
        firstName: '',
        lastName: '',
        idType: 'PASSPORT',
        idDocument: null,
        selfie: null
      });
      setSelfieCapture(null);
      setConsentChecked(false);
      
    } catch (error) {
      console.error('Error submitting verification:', error);
      toast({
        title: "Submission Failed",
        description: "There was an error submitting your verification request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Render appropriate verification form based on business type
  const renderVerificationForm = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-700"></div>
        </div>
      );
    }
    
    // Show verification status based on API response
    if (verificationStatus) {
      if (verificationStatus.status === 'COMPLETED') {
        return (
          <Alert className="bg-green-50 border-green-200">
            <CheckCircle2 className="h-5 w-5 text-green-600" />
            <AlertTitle className="text-green-800 font-medium">Identity Verified</AlertTitle>
            <AlertDescription className="text-green-700">
              Your identity has been successfully verified on {verificationStatus.verificationDate && 
                format(new Date(verificationStatus.verificationDate), 'MMMM d, yyyy')}. 
              You have full access to all platform features.
            </AlertDescription>
          </Alert>
        );
      }
      
      if (verificationStatus.status === 'IN_PROGRESS') {
        return (
          <Alert className="bg-blue-50 border-blue-200">
            <Clock className="h-5 w-5 text-blue-600" />
            <AlertTitle className="text-blue-800 font-medium">Verification In Progress</AlertTitle>
            <AlertDescription className="text-blue-700">
              Your verification request submitted on {verificationStatus.verificationDate && 
                format(new Date(verificationStatus.verificationDate), 'MMMM d, yyyy')} is being processed. 
              This typically takes 1-2 business days. We'll notify you once it's complete.
            </AlertDescription>
          </Alert>
        );
      }
      
      if (verificationStatus.status === 'FAILED') {
        return (
          <Alert className="bg-red-50 border-red-200">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <AlertTitle className="text-red-800 font-medium">Verification Failed</AlertTitle>
            <AlertDescription className="text-red-700">
              Your verification could not be processed. Please check your information and try again.
            </AlertDescription>
          </Alert>
        );
      }
    }
    
    // If no verification status or NOT_STARTED, show the form
    if (dashboardData?.businessType === 'COMPANY') {
      return (
        <form onSubmit={handleSubmit}>
          <div className="space-y-6">
            {/* Status banner for NOT_STARTED */}
            {verificationStatus?.status === 'NOT_STARTED' && (
              <Alert className="bg-amber-50 border-amber-200 mb-6">
                <AlertTriangle className="h-5 w-5 text-amber-600" />
                <AlertTitle className="text-amber-800 font-medium">Verification Required</AlertTitle>
                <AlertDescription className="text-amber-700">
                  Please complete the verification process to access all platform features.
                </AlertDescription>
              </Alert>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="businessName" className="text-gray-900 font-medium">Business Name</Label>
                <Input
                  id="businessName"
                  name="businessName"
                  value={businessData.businessName}
                  onChange={handleBusinessInputChange}
                  className="bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="neq" className="text-gray-900 font-medium">NEQ (Numéro d'entreprise du Québec)</Label>
                <Input
                  id="neq"
                  name="neq"
                  value={businessData.neq}
                  onChange={handleBusinessInputChange}
                  className="bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  required
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="registrationNumber" className="text-gray-900 font-medium">Business Registration Number (if applicable)</Label>
                <Input
                  id="registrationNumber"
                  name="registrationNumber"
                  value={businessData.registrationNumber}
                  onChange={handleBusinessInputChange}
                  className="bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="legalStructure" className="text-gray-900 font-medium">Legal Structure</Label>
                <Select
                  value={businessData.legalStructure}
                  onValueChange={handleLegalStructureChange}
                >
                  <SelectTrigger id="legalStructure" className="bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    <SelectValue placeholder="Select legal structure" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="corporation">Corporation</SelectItem>
                    <SelectItem value="partnership">Partnership</SelectItem>
                    <SelectItem value="soleProprietorship">Sole Proprietorship</SelectItem>
                    <SelectItem value="llc">LLC</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description" className="text-gray-900 font-medium">Business Description</Label>
              <Input
                id="description"
                name="description"
                placeholder="Brief description of your business"
                value={businessData.description}
                onChange={handleBusinessInputChange}
                className="bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label className="text-gray-900 font-medium">Business Registration Documents</Label>
              <Alert className="bg-blue-50 border-blue-200">
                <FileText className="h-4 w-4 text-blue-600" />
                <AlertDescription className="text-blue-800">
                  Please upload at least one of the following documents: Certificate of Incorporation,
                  Articles of Incorporation, Business License, or Tax Registration (GST/QST Number Certificate).
                </AlertDescription>
              </Alert>

              <div className="mt-4 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:bg-gray-50 transition-colors">
                <input
                  type="file"
                  id="documents"
                  multiple
                  className="hidden"
                  onChange={handleFileChange}
                  accept=".pdf,.jpg,.jpeg,.png"
                />
                <label htmlFor="documents" className="cursor-pointer">
                  <Upload className="h-6 w-6 mx-auto text-gray-400" />
                  <p className="mt-2 text-sm font-medium text-gray-900">Click to upload or drag and drop</p>
                  <p className="text-xs text-gray-500">PDF, JPG or PNG (max 10MB per file)</p>
                </label>
              </div>
              
              {fileNames.length > 0 && (
                <div className="mt-4 space-y-2">
                  <p className="text-sm font-medium text-gray-700">Selected files:</p>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {fileNames.map((name, index) => (
                      <li key={index} className="flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-gray-400" />
                        {name}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <Label className="text-gray-700">Consent</Label>
              <div className="flex items-start space-x-2">
                <Checkbox 
                  id="consent" 
                  checked={consentChecked}
                  onCheckedChange={(checked) => setConsentChecked(checked as boolean)}
                  className="mt-1 data-[state=checked]:bg-purple-600"
                />
                <Label htmlFor="consent" className="text-gray-700 text-sm font-normal">
                  I authorize Vestral to collect, process, and verify my identity information for authentication and fraud prevention, in accordance with its privacy policy.
                </Label>
              </div>
            </div>
            
            <Button
              type="submit"
              className="w-full md:w-auto bg-purple-600 hover:bg-purple-700 text-white"
              disabled={submitting || !consentChecked || selectedFiles.length === 0}
            >
              {submitting ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  Submitting...
                </>
              ) : (
                <>
                  <ShieldCheck className="mr-2 h-4 w-4" />
                  Submit Verification
                </>
              )}
            </Button>
          </div>
        </form>
      );
    } else {
      // Individual verification form
      return (
        <form onSubmit={handleSubmit}>
          <div className="space-y-6">
            {/* Status banner for NOT_STARTED */}
            {verificationStatus?.status === 'NOT_STARTED' && (
              <Alert className="bg-amber-50 border-amber-200 mb-6">
                <AlertTriangle className="h-5 w-5 text-amber-600" />
                <AlertTitle className="text-amber-800 font-medium">Verification Required</AlertTitle>
                <AlertDescription className="text-amber-700">
                  Please complete the verification process to access all platform features.
                </AlertDescription>
              </Alert>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="text-gray-900 font-medium">First Name</Label>
                <Input
                  id="firstName"
                  name="firstName"
                  value={individualData.firstName}
                  onChange={handleIndividualInputChange}
                  className="bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName" className="text-gray-900 font-medium">Last Name</Label>
                <Input
                  id="lastName"
                  name="lastName"
                  value={individualData.lastName}
                  onChange={handleIndividualInputChange}
                  className="bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  required
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="idType" className="text-gray-900 font-medium">ID Type</Label>
              <Select
                value={individualData.idType}
                onValueChange={(value) => setIndividualData(prev => ({...prev, idType: value as string}))}
              >
                <SelectTrigger id="idType" className="bg-gray-50 border-gray-200 focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                  <SelectValue placeholder="Select ID type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PASSPORT">Passport</SelectItem>
                  <SelectItem value="DRIVING_LICENSE">Driver's License</SelectItem>
                  <SelectItem value="NATIONAL_ID">National ID</SelectItem>
                  <SelectItem value="OTHER">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label className="text-gray-900 font-medium">ID Document</Label>
              <Alert className="bg-blue-50 border-blue-200">
                <FileText className="h-4 w-4 text-blue-600" />
                <AlertDescription className="text-blue-800">
                  Please upload a clear photo of your ID document (passport, driver's license, or national ID).
                </AlertDescription>
              </Alert>
              
              <div className="mt-4 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:bg-gray-50 transition-colors">
                {individualData.idDocument ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-center">
                      <div className="bg-green-100 rounded-full p-3">
                        <CheckCircle2 className="h-6 w-6 text-green-600" />
                      </div>
                    </div>
                    <p className="text-sm font-medium text-gray-900">{individualData.idDocument.name}</p>
                    <p className="text-xs text-gray-500">{(individualData.idDocument.size / 1024 / 1024).toFixed(2)} MB</p>
                    <Button
                      type="button"
                      variant="outline"
                      className="border-red-300 text-red-600 hover:bg-red-50"
                      onClick={() => {
                        setIndividualData(prev => ({...prev, idDocument: null}));

                        // Reset file input
                        const fileInput = document.getElementById('idDocument') as HTMLInputElement;
                        if (fileInput) fileInput.value = '';
                      }}
                    >
                      Remove Document
                    </Button>
                  </div>
                ) : (
                  <div>
                    <input
                      type="file"
                      id="idDocument"
                      className="hidden"
                      accept="image/*,.pdf"
                      onChange={(e) => handleFileChange(e, 'idDocument')}
                    />
                    <label htmlFor="idDocument" className="w-full h-full flex flex-col items-center justify-center cursor-pointer">
                      <div className="h-12 w-12 rounded-lg bg-gray-100 flex items-center justify-center mb-3">
                        <Upload className="h-6 w-6 text-gray-400" />
                      </div>
                      <p className="text-sm font-medium text-gray-900">Upload ID Document</p>
                      <p className="text-xs text-gray-500">Click to browse or drag and drop</p>
                    </label>
                  </div>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label className="text-gray-900 font-medium">Selfie</Label>
              <Alert className="bg-blue-50 border-blue-200">
                <FileText className="h-4 w-4 text-blue-600" />
                <AlertDescription className="text-blue-800">
                  Please take a clear selfie of yourself holding your ID document next to your face.
                </AlertDescription>
              </Alert>
              
              {isCameraOpen ? (
                <div className="relative border-2 border-gray-300 rounded-lg overflow-hidden">
                  <div className="flex justify-center bg-black">
                    <Webcam 
                      ref={webcamRef} 
                      audio={false} 
                      screenshotFormat="image/jpeg"
                      className="h-[400px] object-contain"
                      videoConstraints={{
                        facingMode: "user",
                        width: { ideal: 1080 },
                        height: { ideal: 1080 },
                        aspectRatio: 1
                      }}
                      mirrored={true}
                      forceScreenshotSourceSize={true}
                    />
                  </div>
                  <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-4">
                    <Button
                      type="button"
                      className="bg-purple-600 hover:bg-purple-700 text-white"
                      onClick={captureSelfie}
                    >
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <circle cx="10" cy="10" r="8" />
                        </svg>
                        Capture
                      </div>
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      className="bg-white border-gray-300 text-gray-700 hover:bg-gray-50"
                      onClick={() => setIsCameraOpen(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="mt-4 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:bg-gray-50 transition-colors">
                  {selfieCapture ? (
                    <div className="space-y-4">
                      <div className="flex justify-center bg-black">
                        <img 
                          src={selfieCapture} 
                          alt="Captured selfie" 
                          className="h-[400px] object-contain rounded-lg mx-auto" 
                        />
                      </div>
                      <div className="flex justify-center space-x-4">
                        <Button
                          type="button"
                          variant="outline"
                          className="border-purple-300 text-purple-600 hover:bg-purple-50"
                          onClick={retakeSelfie}
                        >
                          Retake Photo
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <button
                      type="button"
                      className="w-full h-full flex flex-col items-center justify-center cursor-pointer"
                      onClick={startCamera}
                    >
                      <div className="h-12 w-12 rounded-lg bg-gray-100 flex items-center justify-center mb-3">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      </div>
                      <p className="text-sm font-medium text-gray-900">Take a Selfie</p>
                      <p className="text-xs text-gray-500 mt-1">Click to access your camera</p>
                    </button>
                  )}
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <Label className="text-gray-700">Consent</Label>
              <div className="flex items-start space-x-2">
                <Checkbox 
                  id="consent" 
                  checked={consentChecked}
                  onCheckedChange={(checked) => setConsentChecked(checked as boolean)}
                  className="mt-1 data-[state=checked]:bg-purple-600"
                />
                <Label htmlFor="consent" className="text-gray-700 text-sm font-normal">
                  I authorize Vestral to collect, process, and verify my identity information for authentication and fraud prevention, in accordance with its privacy policy.
                </Label>
              </div>
            </div>
            
            <Button
              type="submit"
              className="w-full md:w-auto bg-purple-600 hover:bg-purple-700 text-white"
              disabled={submitting || !consentChecked || !individualData.idDocument}
            >
              {submitting ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  Submitting...
                </>
              ) : (
                <>
                  <ShieldCheck className="mr-2 h-4 w-4" />
                  Submit Verification
                </>
              )}
            </Button>
          </div>
        </form>
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center gap-4">
          <div className="h-10 w-10 rounded-lg bg-purple-600 flex items-center justify-center">
            <ShieldCheck className="h-5 w-5 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Identity Verification</h1>
            <p className="text-gray-600 mt-1">
              Complete your identity verification to unlock all features of the platform
            </p>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center">
            <ShieldCheck className="h-6 w-6 text-gray-400 animate-pulse" />
          </div>
        </div>
      ) : error ? (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : (
        <div className="bg-white rounded-lg border border-gray-200">
          {/* Header section for forms */}
          {(!verificationStatus || verificationStatus.status === 'NOT_STARTED') && (
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                {dashboardData?.businessType === 'COMPANY' ? 'Business Verification' : 'Individual Verification'}
              </h2>
              <p className="text-gray-600 text-sm mt-1">
                {dashboardData?.businessType === 'COMPANY'
                  ? 'Provide your business details and upload required documents'
                  : 'Provide your personal details and upload identification documents'}
              </p>
            </div>
          )}
          <div className={`${(!verificationStatus || verificationStatus.status === 'NOT_STARTED') ? 'p-6' : 'p-6'}`}>
            {/* Verification Status */}
            {verificationStatus && verificationStatus.status !== 'NOT_STARTED' && (
              <div className="p-6">
                <div className="flex items-center space-x-4">
                  {verificationStatus.status === 'COMPLETED' ? (
                    <div className="bg-green-100 p-3 rounded-lg">
                      <CheckCircle2 className="h-6 w-6 text-green-600" />
                    </div>
                  ) : verificationStatus.status === 'IN_PROGRESS' ? (
                    <div className="bg-blue-100 p-3 rounded-lg">
                      <Clock className="h-6 w-6 text-blue-600" />
                    </div>
                  ) : (
                    <div className="bg-gray-100 p-3 rounded-lg">
                      <AlertCircle className="h-6 w-6 text-gray-600" />
                    </div>
                  )}
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      {verificationStatus.status === 'COMPLETED'
                        ? 'Verification Complete'
                        : verificationStatus.status === 'IN_PROGRESS'
                        ? 'Verification In Progress'
                        : 'Verification Required'}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {verificationStatus.status === 'COMPLETED'
                        ? 'Your identity has been verified successfully.'
                        : verificationStatus.status === 'IN_PROGRESS'
                        ? 'Your verification is being processed. This typically takes 1-2 business days.'
                        : 'Please complete the verification process to access all features.'}
                    </p>
                    {verificationStatus.verificationDate && (
                      <p className="text-xs text-gray-500 mt-1">
                        {verificationStatus.status === 'COMPLETED'
                          ? `Verified on: ${new Date(verificationStatus.verificationDate).toLocaleDateString()}`
                          : verificationStatus.status === 'IN_PROGRESS'
                          ? `Submitted on: ${new Date(verificationStatus.verificationDate).toLocaleDateString()}`
                          : ''}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Verification Form */}
            {showVerificationForm && renderVerificationForm()}
          </div>
        </div>
      )}
    </div>
  )
}
