"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RecentActivity } from "@/components/dashboard/recent-activity"
import { Building2, Eye, FileText, ExternalLink } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { Button } from "@/components/ui/button"
import Cookies from "js-cookie"
import { accountsService } from "@/lib/services/accounts.service"

// Import the DashboardData interface from the service
import type { DashboardData } from "@/lib/services/accounts.service"

export default function DashboardPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch dashboard data from API
  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Get the token from cookies - using correct cookie names from AWS Cognito
        const idToken = Cookies.get('id_token');
        const accessToken = Cookies.get('access_token');
        const token = idToken || accessToken;
        
        if (!token) {
          setError("You are not authenticated. Please log in again.");
          router.push('/login');
          return;
        }
        
        const response = await accountsService.getDashboardData();
        
        if (response.status === 401 || response.status === 403) {
          // Token expired or invalid
          setError("Your session has expired. Please log in again.");
          router.push('/login');
          return;
        }
        
        if (response.error) {
          throw new Error(response.error);
        }
        
        setDashboardData(response.data);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError("Failed to load dashboard data. Please try again.");
        toast({
          title: "Error",
          description: "Failed to load dashboard data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchDashboardData();
  }, [router, toast]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">
              Welcome back, {dashboardData?.name || "User"}
            </h1>
            <p className="text-gray-600 mt-1">
              Here's what's happening with your properties today.
            </p>
          </div>
          {!loading && dashboardData && !dashboardData.idVerified && (
            <Button size="sm" className="bg-purple-600 hover:bg-purple-700 text-white">
              <ExternalLink className="mr-2 h-4 w-4" />
              Verify Identity
            </Button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="bg-white border border-gray-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Active Properties</CardTitle>
            <Building2 className="h-4 w-4 text-gray-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{loading ? "..." : dashboardData?.activePropertiesNumber || 0}</div>
            <p className="text-xs text-gray-500 mt-1">properties currently active</p>
          </CardContent>
        </Card>
        <Card className="bg-white border border-gray-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Property Visits</CardTitle>
            <Eye className="h-4 w-4 text-gray-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{loading ? "..." : dashboardData?.visitNumber || 0}</div>
            <p className="text-xs text-gray-500 mt-1">property visits scheduled</p>
          </CardContent>
        </Card>
        <Card className="bg-white border border-gray-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Applications</CardTitle>
            <FileText className="h-4 w-4 text-gray-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{loading ? "..." : dashboardData?.applicationNumber || 0}</div>
            <p className="text-xs text-gray-500 mt-1">tenant applications received</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity Card */}
      <Card className="bg-white border border-gray-200">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">Recent Activity</CardTitle>
          <CardDescription className="text-gray-500">
            Your recent property management activities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RecentActivity />
        </CardContent>
      </Card>
    </div>
  )
}