# Square Payment Integration Setup

This document explains how to set up Square payment processing for the Vestral Manager application.

## Prerequisites

1. A Square Developer Account
2. A Square Application (Sandbox and/or Production)
3. Access to your Square Dashboard

## Setup Steps

### 1. Create a Square Developer Account

1. Go to [Square Developer Dashboard](https://developer.squareup.com/)
2. Sign up or log in with your Square account
3. Create a new application

### 2. Get Your Credentials

From your Square Developer Dashboard, you'll need:

#### Sandbox Credentials (for testing)
- **Application ID**: Found in your app's "Credentials" tab (starts with `sandbox-sq0idb-`)
- **Location ID**: Found in your app's "Locations" tab
- **Access Token**: Found in your app's "Credentials" tab (starts with `EAAAE...`)

#### Production Credentials (for live payments)
- **Application ID**: Found in your app's "Credentials" tab (starts with `sq0idp-`)
- **Location ID**: Found in your app's "Locations" tab
- **Access Token**: Found in your app's "Credentials" tab (starts with `EAAAE...`)

### 3. Configure Environment Variables

Copy `.env.example` to `.env.local` and update the Square configuration:

```bash
# Square Payment Configuration (Sandbox)
NEXT_PUBLIC_SQUARE_APPLICATION_ID=sandbox-sq0idb-your-actual-app-id
NEXT_PUBLIC_SQUARE_LOCATION_ID=your-actual-location-id
SQUARE_ACCESS_TOKEN=your-actual-access-token
```

### 4. Install Square SDK (Optional - for backend processing)

If you want to implement the full backend payment processing:

```bash
npm install squareup
```

### 5. Update Payment Processing

The current implementation includes:

1. **Frontend**: Square Web Payments SDK integration
2. **Backend**: Mock payment processing (needs real Square API integration)

To implement real payment processing, update `app/api/payments/process/route.ts` with the actual Square SDK calls.

## Testing

### Test Card Numbers

Square provides test card numbers for sandbox testing:

- **Visa**: 4111 1111 1111 1111
- **Mastercard**: 5555 5555 5555 4444
- **American Express**: 3782 822463 10005
- **Discover**: 6011 1111 1111 1117

### Test Details
- **CVV**: Any 3-4 digit number
- **Expiration**: Any future date
- **ZIP Code**: Any valid ZIP code

## Security Notes

1. **Never expose your Access Token** in client-side code
2. **Use HTTPS** in production
3. **Validate payments** on your backend
4. **Store sensitive data securely**

## Production Checklist

- [ ] Switch to production Square credentials
- [ ] Update Square Web Payments SDK URL (remove 'sandbox-' prefix)
- [ ] Implement real backend payment processing
- [ ] Set up webhook endpoints for payment notifications
- [ ] Configure proper error handling and logging
- [ ] Test with real payment methods
- [ ] Set up monitoring and alerts

## Troubleshooting

### Common Issues

1. **"Square.js failed to load"**
   - Check your internet connection
   - Verify the Square SDK script is loading correctly
   - Check browser console for errors

2. **"Invalid Application ID"**
   - Verify your Application ID is correct
   - Make sure you're using the right environment (sandbox vs production)

3. **"Payment failed"**
   - Check your Access Token permissions
   - Verify the Location ID is correct
   - Check the payment amount format (should be in cents)

### Support

- [Square Developer Documentation](https://developer.squareup.com/docs)
- [Square Developer Community](https://developer.squareup.com/forums)
- [Square Support](https://squareup.com/help)

## Implementation Details

### Frontend Components

- `components/payment/SquarePaymentForm.tsx`: Main payment form component
- Square Web Payments SDK: Handles secure card tokenization

### Backend API

- `app/api/payments/process/route.ts`: Payment processing endpoint
- Handles payment creation and verification

### Security Features

- PCI DSS compliant card tokenization
- Secure payment processing
- No sensitive card data stored locally
- Encrypted communication with Square servers
