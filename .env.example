# API URLs
NEXT_PUBLIC_API_URL=https://api.vestral.com
NEXT_PUBLIC_BASE_URL=https://managers.vestral.com

# Environment
NEXT_PUBLIC_ENV=production

# AWS Cognito Authentication
NEXT_PUBLIC_COGNITO_DOMAIN=ca-central-1mzuuzispv.auth.ca-central-1.amazoncognito.com
NEXT_PUBLIC_COGNITO_CLIENT_ID=58km7m63f6tnlilr3kg4kq3ht8
NEXT_PUBLIC_COGNITO_CLIENT_SECRET=o9c6hqheg9kive9ugu2je23j1rv02qb0fjn2ql2cscv3or7n3d1
NEXT_PUBLIC_COGNITO_USER_POOL_ID=ca-central-1_MzUUZiSpV
NEXT_PUBLIC_COGNITO_REGION=ca-central-1

# Mapbox Token
NEXT_PUBLIC_MAPBOX_TOKEN=pk.eyJ1IjoiZWxtb2hhZGViIiwiYSI6ImNtNHQ2dnZwYzA5MHcya3B5YjJoN3B5aTUifQ.OJadTL-8skKILjTUCTtY7w

# Square Payment Configuration
# Get these from your Square Developer Dashboard: https://developer.squareup.com/
# Sandbox values (for testing)
NEXT_PUBLIC_SQUARE_APPLICATION_ID=sandbox-sq0idb-Ini18hSnlgCtQaBBS0RYbA
NEXT_PUBLIC_SQUARE_LOCATION_ID=L47ZS5ZNVBCN7
SQUARE_ACCESS_TOKEN=****************************************************************

REACT_VITE_SQUARE_APP_ID=sandbox-sq0idb-Ini18hSnlgCtQaBBS0RYbA
REACT_VITE_SQUARE_LOCATION_ID=L47ZS5ZNVBCN7
# Production values (uncomment for production)
# NEXT_PUBLIC_SQUARE_APPLICATION_ID=sq0idp-your-production-app-id
# NEXT_PUBLIC_SQUARE_LOCATION_ID=your-production-location-id
# SQUARE_ACCESS_TOKEN=your-production-access-token

# Available environments: development, staging, production
# To use different environments, copy this file to .env.local and change the values
# Examples:
# 
# Development (local):
# NEXT_PUBLIC_API_URL=http://localhost:8081
# NEXT_PUBLIC_ENV=development
#
# Staging:
# NEXT_PUBLIC_API_URL=https://api-staging.vestral.com
# NEXT_PUBLIC_ENV=staging
# 
# Production:
# NEXT_PUBLIC_API_URL=https://api.vestral.com
# NEXT_PUBLIC_ENV=production
