"use client"

import { useEffect, useRef, useState } from 'react';
import { Button } from '@/components/ui/button';

// Square Web Payments SDK types
declare global {
  interface Window {
    Square?: {
      payments: (appId: string, locationId: string) => {
        card: () => Promise<any>;
        build: () => Promise<any>;
      };
    };
  }
}

interface SquarePaymentFormProps {
  amount: number;
  onPaymentSuccess: (result: any) => void;
  onPaymentError: (error: string) => void;
  loading: boolean;
  disabled?: boolean;
}

export function SquarePaymentForm({ 
  amount, 
  onPaymentSuccess, 
  onPaymentError, 
  loading,
  disabled = false 
}: SquarePaymentFormProps) {
  const cardRef = useRef<HTMLDivElement>(null);
  const [card, setCard] = useState<any>(null);
  const [payments, setPayments] = useState<any>(null);
  const [isSquareLoaded, setIsSquareLoaded] = useState(false);

  // Square configuration - these should be environment variables
  const APPLICATION_ID = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID || 'sandbox-sq0idb-Ini18hSnlgCtQaBBS0RYbA';
  const LOCATION_ID = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID || 'L47ZS5ZNVBCN7';

  useEffect(() => {
    const initializeSquare = async () => {
      if (!window.Square) {
        console.error('Square.js failed to load properly');
        onPaymentError('Payment system failed to load. Please refresh the page and try again.');
        return;
      }

      try {
        const paymentsInstance = window.Square.payments(APPLICATION_ID, LOCATION_ID);
        setPayments(paymentsInstance);

        const cardInstance = await paymentsInstance.card();
        await cardInstance.attach(cardRef.current);
        setCard(cardInstance);
        setIsSquareLoaded(true);
      } catch (error) {
        console.error('Failed to initialize Square payments:', error);
        onPaymentError('Failed to initialize payment form. Please try again.');
      }
    };

    // Wait for Square to load
    const checkSquareLoaded = () => {
      if (window.Square) {
        initializeSquare();
      } else {
        setTimeout(checkSquareLoaded, 100);
      }
    };

    checkSquareLoaded();

    // Cleanup
    return () => {
      if (card) {
        card.destroy();
      }
    };
  }, [APPLICATION_ID, LOCATION_ID, onPaymentError]);

  const handlePayment = async () => {
    if (!card || !payments) {
      onPaymentError('Payment form not ready. Please try again.');
      return;
    }

    try {
      // Tokenize the payment method
      const result = await card.tokenize();
      
      if (result.status === 'OK') {
        // Send the token to your backend for processing
        const paymentResult = await processPayment(result.token, amount);
        onPaymentSuccess(paymentResult);
      } else {
        console.error('Tokenization failed:', result.errors);
        onPaymentError(result.errors?.[0]?.message || 'Payment failed. Please check your card details.');
      }
    } catch (error) {
      console.error('Payment error:', error);
      onPaymentError('Payment processing failed. Please try again.');
    }
  };

  // This function should call your backend API to process the payment
  const processPayment = async (token: string, amount: number) => {
    // TODO: Replace with your actual backend API call
    const response = await fetch('/api/payments/process', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        source_id: token,
        amount_money: {
          amount: Math.round(amount * 100), // Convert to cents
          currency: 'USD'
        },
        idempotency_key: crypto.randomUUID()
      }),
    });

    if (!response.ok) {
      throw new Error('Payment processing failed');
    }

    return await response.json();
  };

  if (!isSquareLoaded) {
    return (
      <div className="space-y-4">
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-10 bg-gray-200 rounded mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-10 bg-gray-200 rounded mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-10 bg-gray-200 rounded"></div>
          </div>
        </div>
        <p className="text-sm text-gray-600 text-center">Loading secure payment form...</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Square Card Form */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div ref={cardRef} id="card-container" className="min-h-[200px]">
          {/* Square card form will be injected here */}
        </div>
      </div>

      {/* Payment Button */}
      <Button
        onClick={handlePayment}
        disabled={loading || disabled || !isSquareLoaded}
        className="w-full bg-green-600 hover:bg-green-700 text-white py-3 text-lg font-semibold"
      >
        {loading ? (
          <>
            <div className="mr-2 h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
            Processing Payment...
          </>
        ) : (
          <>
            Pay ${amount.toFixed(2)}
          </>
        )}
      </Button>

      {/* Security Notice */}
      <div className="text-xs text-gray-500 text-center space-y-1">
        <p>🔒 Your payment information is secure and encrypted</p>
        <p>Powered by Square • PCI DSS Compliant</p>
      </div>
    </div>
  );
}

// Default export
export default SquarePaymentForm;
