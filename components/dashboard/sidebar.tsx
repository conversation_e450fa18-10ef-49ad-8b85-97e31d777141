import { useState, useEffect } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  HomeIcon,
  FileText,
  Shield,
  User,
  Settings as SettingsIcon,
  Building2,
  ShieldCheck,
  BarChart3,
  HelpCircle
} from "lucide-react"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  collapsed?: boolean;
}

export function Sidebar({ className, collapsed: propCollapsed }: SidebarProps) {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(propCollapsed || false)

  // Sync with prop changes
  useEffect(() => {
    if (propCollapsed !== undefined) {
      setIsCollapsed(propCollapsed)
    }
  }, [propCollapsed])

  // Group navigation items by category
  const navigationGroups = [{
    category: "MAIN",
    items: [
      {
        name: "Overview",
        href: "/dashboard",
        icon: HomeIcon,
        current: pathname === "/dashboard",
      },
      {
        name: "Properties",
        href: "/dashboard/properties",
        icon: Building2,
        current: pathname.startsWith("/dashboard/properties"),
      },
      
      {
        name: "Leases",
        href: "/dashboard/leases",
        icon: FileText,
        current: pathname.startsWith("/dashboard/leases"),
      },
      
    ]
  }, {
    category: "MARKET DATA",
    items: [
      {
        name: "Quebec Rental Market",
        href: "/dashboard/market-data/quebec-map",
        icon: BarChart3,
        current: pathname.startsWith("/dashboard/market-data"),
      },
    ]
  }, {
    category: "VERIFICATION",
    items: [
      {
        name: "ID Verification",
        href: "/dashboard/verification",
        icon: ShieldCheck,
        current: pathname === "/dashboard/verification",
        badge: "Required"
      },

      {
        name: "Tenant Verifications",
        href: "/dashboard/verifications",
        icon: Shield,
        current: pathname.startsWith("/dashboard/verifications"),
      },
    ]
  }, {
    category: "SETTINGS",
    items: [
      {
        name: "Profile",
        href: "/dashboard/profile",
        icon: User,
        current: pathname === "/dashboard/profile",
      },
      {
        name: "Settings",
        href: "/dashboard/settings",
        icon: SettingsIcon,
        current: pathname === "/dashboard/settings",
      },
      {
        name: "Help & Support",
        href: "/dashboard/support",
        icon: HelpCircle,
        current: pathname === "/dashboard/support",
      },
    ]
  }]

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Navigation */}

      <nav className="flex-1 overflow-y-auto py-4 px-3">
        {navigationGroups.map((group) => (
          <div key={group.category} className="mb-6">
            {!isCollapsed && (
              <div className="px-3 mb-2">
                <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                  {group.category}
                </h3>
              </div>
            )}
            <ul className="space-y-1">
              {group.items.map((item) => {
                const isActive = item.current;

                return (
                  <TooltipProvider key={item.name} delayDuration={300}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <li>
                          <Link
                            href={item.href}
                            className={cn(
                              "flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors",
                              isActive
                                ? "bg-purple-600 text-white"
                                : "text-gray-700 hover:bg-gray-100 hover:text-gray-900",
                              isCollapsed && "justify-center px-2"
                            )}
                          >
                            <item.icon className="h-5 w-5 flex-shrink-0" />
                            {!isCollapsed && (
                              <span className="ml-3">{item.name}</span>
                            )}
                            {!isCollapsed && 'badge' in item && item.badge && (
                              <span className={cn(
                                "ml-auto inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium",
                                isActive
                                  ? "bg-white/20 text-white"
                                  : "bg-gray-100 text-gray-600"
                              )}>
                                {item.badge}
                              </span>
                            )}
                          </Link>
                        </li>
                      </TooltipTrigger>
                      {isCollapsed && (
                        <TooltipContent side="right" className="bg-gray-900 text-white">
                          <div className="flex flex-col gap-1">
                            <p className="font-medium">{item.name}</p>
                            {'badge' in item && item.badge && (
                              <p className="text-xs text-gray-300">{item.badge}</p>
                            )}
                          </div>
                        </TooltipContent>
                      )}
                    </Tooltip>
                  </TooltipProvider>
                );
              })}
            </ul>
          </div>
        ))}
      </nav>

      {/* Footer Section */}
      <div className="border-t border-gray-200 p-4">
        {!isCollapsed ? (
          <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
              <User className="h-4 w-4 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">John Doe</p>
              <p className="text-xs text-gray-500 truncate">Property Manager</p>
            </div>
          </div>
        ) : (
          <div className="flex justify-center">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center cursor-pointer">
                    <User className="h-4 w-4 text-white" />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <div className="flex flex-col gap-1">
                    <p className="font-medium">John Doe</p>
                    <p className="text-xs text-gray-400">Property Manager</p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}
      </div>
    </div>
  )
}
