"use client"

import { useState, useEffect, useRef } from "react"
import { MapPin, Building2, Bed, Bath, X, Loader2, Navigation } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { PublicProperty } from "@/lib/services/public-properties.service"

// Import Mapbox CSS
import 'mapbox-gl/dist/mapbox-gl.css'

interface PropertyMapProps {
  properties: PublicProperty[]
  className?: string
}

export function PropertyMap({ properties, className = "" }: PropertyMapProps) {
  const mapContainerRef = useRef<HTMLDivElement>(null)
  const mapRef = useRef<any>(null)
  const markersRef = useRef<any[]>([])
  const [selectedProperty, setSelectedProperty] = useState<PublicProperty | null>(null)
  const [selectedCluster, setSelectedCluster] = useState<PublicProperty[] | null>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [mapError, setMapError] = useState<string | null>(null)
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null)
  const [locationPermissionDenied, setLocationPermissionDenied] = useState(false)
  const [initialZoomSet, setInitialZoomSet] = useState(false)

  // Get Mapbox token from environment
  const MAPBOX_TOKEN = process.env.NEXT_PUBLIC_MAPBOX_TOKEN

  // Filter properties with coordinates
  const propertiesWithCoords = properties.filter(property => property.coordinates)

  // Function to calculate distance between two coordinates
  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number) => {
    const R = 6371 // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180
    const dLng = (lng2 - lng1) * Math.PI / 180
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c * 1000 // Distance in meters
  }

  // Function to group nearby properties
  const groupProperties = (properties: PublicProperty[], maxDistance: number = 100) => {
    const groups: Array<{
      lat: number
      lng: number
      properties: PublicProperty[]
      isCluster: boolean
    }> = []

    const processed = new Set<string>()

    properties.forEach(property => {
      if (processed.has(property.id) || !property.coordinates) return

      const group = {
        lat: property.coordinates.lat,
        lng: property.coordinates.lng,
        properties: [property],
        isCluster: false
      }

      // Find nearby properties
      properties.forEach(otherProperty => {
        if (otherProperty.id === property.id ||
            processed.has(otherProperty.id) ||
            !otherProperty.coordinates) return

        const distance = calculateDistance(
          property.coordinates!.lat,
          property.coordinates!.lng,
          otherProperty.coordinates.lat,
          otherProperty.coordinates.lng
        )

        if (distance <= maxDistance) {
          group.properties.push(otherProperty)
          processed.add(otherProperty.id)
        }
      })

      processed.add(property.id)

      // Mark as cluster if more than one property
      if (group.properties.length > 1) {
        group.isCluster = true
        // Calculate center point of cluster
        const avgLat = group.properties.reduce((sum, p) => sum + p.coordinates!.lat, 0) / group.properties.length
        const avgLng = group.properties.reduce((sum, p) => sum + p.coordinates!.lng, 0) / group.properties.length
        group.lat = avgLat
        group.lng = avgLng
      }

      groups.push(group)
    })

    return groups
  }

  // Function to retry getting user location
  const retryLocation = () => {
    setUserLocation(null)
    setLocationPermissionDenied(false)

    console.log('Retrying location detection...')

    if (!navigator.geolocation) {
      console.log('Geolocation not supported')
      setLocationPermissionDenied(true)
      setUserLocation([-73.5673, 45.5017])
      return
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude, accuracy } = position.coords
        console.log('Retry location success:', { latitude, longitude, accuracy })
        setUserLocation([longitude, latitude])
        setLocationPermissionDenied(false)
      },
      (error) => {
        console.error('Retry location failed:', error)
        setLocationPermissionDenied(true)
        setUserLocation([-73.5673, 45.5017])
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0 // Force fresh location
      }
    )
  }

  // Debug logging
  useEffect(() => {
    console.log('Mapbox Token:', MAPBOX_TOKEN ? 'Present' : 'Missing')
    console.log('User Location:', userLocation)
    console.log('Properties with coords:', propertiesWithCoords.length)
  }, [userLocation, propertiesWithCoords])

  // Debug selected property changes
  useEffect(() => {
    if (selectedProperty) {
      console.log('Property selected:', selectedProperty.id, selectedProperty.title)
    } else {
      console.log('Property deselected')
    }
  }, [selectedProperty])

  // Get user's current location
  useEffect(() => {
    const getUserLocation = () => {
      // Check if geolocation is supported
      if (!navigator.geolocation) {
        console.log('Geolocation not supported - using Montreal')
        setLocationPermissionDenied(true)
        setUserLocation([-73.5673, 45.5017]) // Montreal default
        return
      }

      // Try to get user's location
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords
          console.log('User location found:', latitude, longitude)
          setUserLocation([longitude, latitude])
          setLocationPermissionDenied(false)
        },
        () => {
          console.log('Location access denied or failed - using Montreal')
          setLocationPermissionDenied(true)
          setUserLocation([-73.5673, 45.5017]) // Montreal default
        },
        {
          timeout: 10000,
          maximumAge: 300000 // 5 minutes cache
        }
      )
    }

    getUserLocation()
  }, [])

  // Initialize Mapbox map
  useEffect(() => {
    if (!mapContainerRef.current || mapRef.current || !userLocation) return

    // Check if Mapbox token is available
    if (!MAPBOX_TOKEN) {
      setMapError('Mapbox token is missing')
      return
    }

    const initializeMap = async () => {
      try {
        console.log('Initializing map with location:', userLocation)

        // Dynamically import mapbox-gl
        const mapboxgl = await import('mapbox-gl')

        // Set access token
        mapboxgl.default.accessToken = MAPBOX_TOKEN

        // Determine zoom level based on location type
        let zoom = 13 // Default city-level zoom

        if (locationPermissionDenied) {
          zoom = 11 // Wider view for Montreal fallback
          console.log('Using Montreal fallback with zoom:', zoom)
        } else {
          zoom = 13 // Good city-level zoom for user's location
          console.log('Using user location with zoom:', zoom)
        }

        // Create map centered on user location with appropriate zoom
        const map = new mapboxgl.default.Map({
          container: mapContainerRef.current!,
          style: 'mapbox://styles/mapbox/streets-v12',
          center: userLocation,
          zoom: zoom,
          attributionControl: false
        })

        // Add error handling for map
        map.on('error', (e) => {
          console.error('Mapbox error:', e)
          setMapError(`Map error: ${e.error?.message || 'Unknown error'}`)
        })

        // Add navigation controls
        map.addControl(new mapboxgl.default.NavigationControl(), 'top-right')

        // Add user location marker if using actual GPS location
        if (!locationPermissionDenied) {
          const userMarkerEl = document.createElement('div')
          userMarkerEl.style.cssText = `
            width: 24px;
            height: 24px;
            background-color: #3B82F6;
            border: 3px solid white;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: bold;
            font-size: 12px;
            color: white;
          `

          userMarkerEl.textContent = 'V'

          new mapboxgl.default.Marker({
            element: userMarkerEl,
            anchor: 'center' // Center anchor for user location
          })
            .setLngLat(userLocation)
            .addTo(map)
        }

        // Wait for map to load
        map.on('load', () => {
          setMapLoaded(true)
          setInitialZoomSet(true) // Mark that initial zoom is set

          // Add property markers
          addMarkersToMap(map, mapboxgl.default)

          // Only adjust view for properties if user explicitly wants to see them
          // Don't auto-zoom on initial load to preserve user location zoom
        })

        mapRef.current = map

      } catch (error) {
        console.error('Error initializing Mapbox:', error)
        setMapError('Failed to load map')
      }
    }

    initializeMap()

    // Cleanup
    return () => {
      if (mapRef.current) {
        mapRef.current.remove()
        mapRef.current = null
      }
    }
  }, [userLocation]) // Initialize when userLocation is available

  // Add markers to map
  const addMarkersToMap = (map: any, mapboxgl: any) => {
    // Clear existing markers
    markersRef.current.forEach(marker => marker.remove())
    markersRef.current = []

    // Group properties by proximity
    const propertyGroups = groupProperties(propertiesWithCoords, 100) // 100 meters

    // Add markers for each group
    propertyGroups.forEach((group) => {
      // Create marker element
      const el = document.createElement('div')
      el.className = 'property-marker'

      if (group.isCluster) {
        // Cluster marker styling
        el.style.cssText = `
          width: 50px;
          height: 50px;
          background-color: #DC2626;
          border: 3px solid white;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 12px rgba(0,0,0,0.3);
          transition: all 0.2s ease;
          position: relative;
          z-index: 999;
          font-weight: bold;
          color: white;
          font-size: 14px;
        `

        // Show number of properties in cluster
        el.innerHTML = `${group.properties.length}`

        // Cluster hover effects
        el.addEventListener('mouseenter', () => {
          el.style.width = '55px'
          el.style.height = '55px'
          el.style.backgroundColor = '#B91C1C'
          el.style.zIndex = '1000'
          el.style.boxShadow = '0 6px 16px rgba(0,0,0,0.4)'
        })

        el.addEventListener('mouseleave', () => {
          el.style.width = '50px'
          el.style.height = '50px'
          el.style.backgroundColor = '#DC2626'
          el.style.zIndex = '999'
          el.style.boxShadow = '0 4px 12px rgba(0,0,0,0.3)'
        })

        // Cluster click handler - show cluster popup
        el.addEventListener('click', (e) => {
          e.stopPropagation()
          console.log('Cluster clicked with', group.properties.length, 'properties')
          setSelectedCluster(group.properties)
          setSelectedProperty(null) // Clear single property selection
        })

      } else {
        // Create solid purple V-shaped marker
        el.style.cssText = `
          width: 40px;
          height: 48px;
          cursor: pointer;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
        `

        // Create simple, guaranteed visible purple marker
        el.style.cssText = `
          width: 40px;
          height: 40px;
          background-color: #8B5CF6;
          border: 4px solid white;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 12px rgba(0,0,0,0.3);
          transition: all 0.3s ease;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-weight: 900;
          font-size: 18px;
          color: white;
          text-shadow: 0 2px 4px rgba(0,0,0,0.7);
        `

        // Add the V text directly
        el.textContent = 'V'

        // Add building icon for single property
        el.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
            <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"/>
            <path d="M6 12h4"/>
            <path d="M6 16h4"/>
            <path d="M16 12h2"/>
            <path d="M16 16h2"/>
          </svg>
        `

        // Fixed hover effects without position changes
        el.addEventListener('mouseenter', () => {
          el.style.backgroundColor = '#7C3AED'
          el.style.boxShadow = '0 6px 16px rgba(124, 58, 237, 0.6)'
          el.style.borderWidth = '5px'  // Slightly thicker border instead of scaling
        })

        el.addEventListener('mouseleave', () => {
          el.style.backgroundColor = '#8B5CF6'
          el.style.boxShadow = '0 4px 12px rgba(0,0,0,0.3)'
          el.style.borderWidth = '4px'  // Original border width
        })

        // Single property click handler
        el.addEventListener('click', (e) => {
          e.stopPropagation()
          const property = group.properties[0]
          console.log('Marker clicked for property:', property.id, property.title)
          setSelectedProperty(property)
        })
      }

      // Create and add marker with proper anchor
      const mapboxMarker = new mapboxgl.Marker({
        element: el,
        anchor: 'bottom' // Anchor at the bottom point of the pin
      })
        .setLngLat([group.lng, group.lat])
        .addTo(map)

      markersRef.current.push(mapboxMarker)
    })
  }

  // Update markers when properties change
  useEffect(() => {
    if (mapRef.current && mapLoaded && initialZoomSet) {
      const mapboxgl = require('mapbox-gl')
      addMarkersToMap(mapRef.current, mapboxgl)

      // Only update markers, preserve the user's current zoom and center
      console.log('Updated property markers without changing zoom/center')
    }
  }, [properties, mapLoaded, initialZoomSet])

  return (
    <div className={`relative ${className}`}>
      {/* Map Container */}
      <div
        ref={mapContainerRef}
        className="relative w-full h-[600px] bg-gray-100 rounded-lg overflow-hidden border border-gray-200"
        style={{
          minHeight: '600px',
          position: 'relative'
        }}
      />

      {/* Loading State */}
      {!mapLoaded && !mapError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
          <div className="text-center">
            <div className="relative mb-4">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
              </div>
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-lg">
                <MapPin className="h-3.5 w-3.5 text-purple-600" />
              </div>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Map</h3>
            <p className="text-gray-600">Preparing property locations...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {mapError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg">
          <div className="text-center max-w-md mx-auto p-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MapPin className="h-8 w-8 text-red-500" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Map Unavailable</h3>
            <p className="text-gray-600 mb-4">{mapError}</p>
            <Button 
              onClick={() => window.location.reload()} 
              variant="outline"
              className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
            >
              Retry
            </Button>
          </div>
        </div>
      )}

      {/* Properties Count Overlay */}
      {mapLoaded && (
        <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg border border-gray-200">
          <div className="text-sm font-semibold text-gray-900">{properties.length} Properties</div>
          <div className="text-xs text-gray-600">Available for rent</div>
        </div>
      )}

      {/* Location Indicator */}
      {mapLoaded && (
        <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg border border-gray-200">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${
                !userLocation ? 'bg-yellow-400 animate-pulse' :
                locationPermissionDenied ? 'bg-gray-400' : 'bg-green-500'
              }`}></div>
              <div>
                <div className="text-sm font-semibold text-gray-900">
                  {!userLocation ? 'Getting Location...' :
                   locationPermissionDenied ? 'Default Location' : 'Your Location'}
                </div>
                <div className="text-xs text-gray-600">
                  {!userLocation ? 'Please wait' :
                   locationPermissionDenied ? 'Montreal, QC' : 'GPS position'}
                </div>
              </div>
            </div>

            {/* Retry Location Button */}
            {locationPermissionDenied && (
              <Button
                variant="ghost"
                size="sm"
                onClick={retryLocation}
                className="h-8 w-8 p-0 hover:bg-blue-50"
                title="Try to get your location again"
              >
                <Navigation className="h-4 w-4 text-blue-600" />
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Property Details Popup */}
      {selectedProperty && (
        <div
          className="absolute inset-0 bg-black/40 backdrop-blur-md flex items-center justify-center p-4"
          style={{ zIndex: 9999 }}
          onClick={(e) => {
            // Close popup when clicking outside
            if (e.target === e.currentTarget) {
              setSelectedProperty(null)
            }
          }}
        >
          <Card className="w-full max-w-sm mx-auto shadow-2xl rounded-3xl overflow-hidden border-0 bg-white/95 backdrop-blur-sm transform transition-all duration-300 scale-100 hover:scale-[1.02]">
            <CardContent className="p-0">
              {/* Property Image */}
              <div className="relative h-44 bg-gradient-to-br from-purple-50 to-purple-100">
                <img
                  src={selectedProperty.images[0]}
                  alt={selectedProperty.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-3 right-3 bg-white/95 hover:bg-white backdrop-blur-sm rounded-full shadow-lg transition-all duration-200 hover:scale-110"
                  onClick={() => setSelectedProperty(null)}
                >
                  <X className="h-4 w-4 text-gray-700" />
                </Button>

                <Badge className="absolute top-3 left-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-full px-3 py-1 text-xs font-semibold shadow-lg">
                  Available Now
                </Badge>
              </div>

              {/* Property Details */}
              <div className="p-5">
                {/* Price */}
                <div className="flex items-center justify-between mb-4">
                  <div className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent">
                    ${selectedProperty.price?.toLocaleString() || '0'}
                  </div>
                  <div className="text-sm text-gray-500 font-medium bg-gray-100 px-3 py-1 rounded-full">
                    per month
                  </div>
                </div>

                {/* Title */}
                <h3 className="font-bold text-lg text-gray-900 line-clamp-2 leading-tight mb-3">
                  {selectedProperty.title}
                </h3>

                {/* Address */}
                <div className="flex items-start gap-2 text-gray-600 mb-4 bg-gray-50 rounded-xl p-3">
                  <MapPin className="h-4 w-4 flex-shrink-0 mt-0.5 text-purple-500" />
                  <span className="text-sm line-clamp-2 leading-relaxed font-medium">{selectedProperty.address}</span>
                </div>

                {/* Room Details */}
                <div className="flex items-center justify-center gap-8 mb-5 bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl py-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                      <Bed className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <div className="text-lg font-bold text-gray-900">{selectedProperty.bedrooms}</div>
                      <div className="text-xs text-gray-500 font-medium">Bedrooms</div>
                    </div>
                  </div>

                  <div className="w-px h-10 bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>

                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                      <Bath className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <div className="text-lg font-bold text-gray-900">{selectedProperty.bathrooms}</div>
                      <div className="text-xs text-gray-500 font-medium">Bathrooms</div>
                    </div>
                  </div>
                </div>

                {/* Description */}
                {selectedProperty.description && (
                  <div className="mb-5">
                    <p className="text-gray-600 text-sm line-clamp-3 leading-relaxed bg-gray-50 rounded-xl p-3">
                      {selectedProperty.description}
                    </p>
                  </div>
                )}

                {/* Action Button */}
                <Link href={`/properties/${selectedProperty.id}`} className="block">
                  <Button
                    className="w-full bg-gradient-to-r from-purple-600 via-purple-700 to-purple-800 hover:from-purple-700 hover:via-purple-800 hover:to-purple-900 text-white shadow-xl hover:shadow-2xl transition-all duration-300 rounded-2xl py-4 text-base font-semibold transform hover:scale-[1.02] active:scale-[0.98] border-0"
                    onClick={() => {
                      console.log('Navigating to property:', selectedProperty.id)
                    }}
                  >
                    <span className="flex items-center justify-center gap-2">
                      View Property Details
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </span>
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Enhanced Cluster Properties Popup */}
      {selectedCluster && (
        <div
          className="absolute inset-0 bg-black/40 backdrop-blur-md flex items-center justify-center p-4"
          style={{ zIndex: 9999 }}
          onClick={(e) => {
            // Close popup when clicking outside
            if (e.target === e.currentTarget) {
              setSelectedCluster(null)
            }
          }}
        >
          <Card className="w-full max-w-lg mx-auto shadow-2xl rounded-3xl overflow-hidden max-h-[85vh] overflow-y-auto border-0 bg-white/95 backdrop-blur-sm">
            <CardContent className="p-0">
              {/* Enhanced Cluster Header */}
              <div className="relative bg-gradient-to-br from-purple-600 via-purple-700 to-purple-800 p-6 text-white">
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-3 right-3 bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full text-white transition-all duration-200 hover:scale-110"
                  onClick={() => setSelectedCluster(null)}
                >
                  <X className="h-5 w-5" />
                </Button>

                <div className="flex items-center gap-3 mb-3">
                  <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                    <span className="text-xl font-bold">{selectedCluster.length}</span>
                  </div>
                  <div>
                    <h3 className="text-2xl font-bold mb-1">
                      Properties Nearby
                    </h3>
                    <p className="text-purple-100 text-sm">Tap any property to view details</p>
                  </div>
                </div>
              </div>

              {/* Enhanced Properties List */}
              <div className="p-5 space-y-3">
                {selectedCluster.map((property) => (
                  <div
                    key={property.id}
                    className="bg-white border border-gray-100 rounded-2xl p-4 hover:border-purple-200 hover:bg-gradient-to-r hover:from-purple-50 hover:to-blue-50 transition-all duration-200 cursor-pointer shadow-sm hover:shadow-md transform hover:scale-[1.01]"
                    onClick={() => {
                      setSelectedProperty(property)
                      setSelectedCluster(null)
                    }}
                  >
                    <div className="flex items-center gap-4">
                      {/* Property Image */}
                      <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl overflow-hidden flex-shrink-0 shadow-sm">
                        {property.images && property.images.length > 0 ? (
                          <img
                            src={property.images[0]}
                            alt={property.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-purple-200 to-purple-300 flex items-center justify-center">
                            <div className="w-6 h-6 bg-purple-600 rounded-md flex items-center justify-center">
                              <span className="text-white text-xs font-bold">V</span>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Property Info */}
                      <div className="flex-1 min-w-0">
                        {/* Title and Price */}
                        <div className="flex items-start justify-between gap-3 mb-2">
                          <h4 className="font-bold text-gray-900 line-clamp-1 text-base">
                            {property.title}
                          </h4>
                          <div className="text-right flex-shrink-0">
                            <div className="text-lg font-bold bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent">
                              ${property.price?.toLocaleString() || '0'}
                            </div>
                            <div className="text-xs text-gray-500">per month</div>
                          </div>
                        </div>

                        {/* Address */}
                        <div className="flex items-center gap-2 text-gray-600 mb-3">
                          <MapPin className="h-3 w-3 flex-shrink-0 text-purple-500" />
                          <span className="text-sm line-clamp-1 font-medium">{property.address}</span>
                        </div>

                        {/* Room Details */}
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-2 bg-purple-50 rounded-lg px-2 py-1">
                            <div className="w-5 h-5 bg-purple-500 rounded-md flex items-center justify-center">
                              <Bed className="h-3 w-3 text-white" />
                            </div>
                            <span className="text-sm font-medium text-gray-700">{property.bedrooms}</span>
                          </div>
                          <div className="flex items-center gap-2 bg-blue-50 rounded-lg px-2 py-1">
                            <div className="w-5 h-5 bg-blue-500 rounded-md flex items-center justify-center">
                              <Bath className="h-3 w-3 text-white" />
                            </div>
                            <span className="text-sm font-medium text-gray-700">{property.bathrooms}</span>
                          </div>
                        </div>
                      </div>

                      {/* White View Button */}
                      <Button
                        size="sm"
                        className="flex-shrink-0 bg-white border border-gray-200 text-gray-700 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-900 rounded-xl px-3 py-2 text-xs font-semibold shadow-sm hover:shadow-md transition-all duration-200"
                        onClick={(e) => {
                          e.stopPropagation()
                          window.open(`/properties/${property.id}`, '_blank')
                        }}
                      >
                        <span className="flex items-center gap-1">
                          View
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                          </svg>
                        </span>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
