"use client"

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  const { isAuthenticated, checkAuth } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(true);
  const [hasRedirected, setHasRedirected] = useState(false);

  useEffect(() => {
    // Don't check auth if we're already on the login page
    if (pathname === '/login' || hasRedirected) {
      setIsLoading(false);
      return;
    }

    const authCheck = checkAuth();

    if (!authCheck && !hasRedirected) {
      setHasRedirected(true);
      router.push('/login');
      return;
    }

    setIsLoading(false);
  }, [checkAuth, router, pathname, hasRedirected]);

  // If we're on the login page, don't show the protected route wrapper
  if (pathname === '/login') {
    return <>{children}</>;
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
          <div className="h-4 w-4 rounded-full bg-purple-600 animate-pulse"></div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated && !hasRedirected) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
          <div className="h-4 w-4 rounded-full bg-purple-600 animate-pulse"></div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
