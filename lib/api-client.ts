import Cookies from 'js-cookie';
import env from '@/lib/env';

interface ApiRequestOptions extends RequestInit {
  requireAuth?: boolean;
}

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private getAuthToken(): string | null {
    const idToken = Cookies.get('id_token');
    const session = Cookies.get('session');
    return idToken || session || null;
  }

  private isTokenExpired(token: string): boolean {
    try {
      // If it's not a JWT (doesn't have dots), consider it valid
      if (!token.includes('.')) {
        return false;
      }

      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      return payload.exp && payload.exp < currentTime;
    } catch (error) {
      // If we can't parse it but it exists, consider it valid (non-JWT tokens)
      return false;
    }
  }

  private handleTokenExpiration(): void {
    // Clear all auth cookies
    Cookies.remove('id_token');
    Cookies.remove('session');
    Cookies.remove('access_token');
    Cookies.remove('refresh_token');

    // Only redirect if we're not already on the login page
    if (typeof window !== 'undefined' && window.location.pathname !== '/login') {
      window.location.href = '/login';
    }
  }

  async request<T>(
    endpoint: string, 
    options: ApiRequestOptions = {}
  ): Promise<T> {
    const { requireAuth = true, headers = {}, ...restOptions } = options;
    
    const url = `${this.baseURL}${endpoint}`;
    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(headers as Record<string, string>),
    };

    // Add authentication header if required
    if (requireAuth) {
      const token = this.getAuthToken();

      if (!token) {
        this.handleTokenExpiration();
        throw new Error('No authentication token found');
      }

      if (this.isTokenExpired(token)) {
        this.handleTokenExpiration();
        throw new Error('Authentication token expired');
      }

      requestHeaders['Authorization'] = `Bearer ${token}`;
    }

    try {
      const response = await fetch(url, {
        ...restOptions,
        headers: requestHeaders,
      });

      // Handle authentication errors
      if (response.status === 401 || response.status === 403) {
        this.handleTokenExpiration();
        throw new Error('Authentication failed');
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Handle empty responses
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      } else {
        return response.text() as unknown as T;
      }
    } catch (error) {
      // Re-throw the error for the calling code to handle
      throw error;
    }
  }

  // Convenience methods
  async get<T>(endpoint: string, options?: ApiRequestOptions): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any, options?: ApiRequestOptions): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any, options?: ApiRequestOptions): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string, options?: ApiRequestOptions): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  async patch<T>(endpoint: string, data?: any, options?: ApiRequestOptions): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient(env.API_URL);

// Export the class for custom instances if needed
export { ApiClient };
